import 'package:flutter/material.dart';
import '../database/databaseservice.dart';
import '../database/widgets/databaseviewerwidget.dart';
import '../widgets/theme/theme.dart';
import '../widgets/approuteswidget.dart';

/// Homepage for MicroRealEstate application
class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  Map<String, dynamic>? _databaseStats;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadDatabaseStats();
  }

  Future<void> _loadDatabaseStats() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final stats = await DatabaseService.instance.getDatabaseStatistics();
      setState(() {
        _databaseStats = stats;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading database stats: $e')),
        );
      }
    }
  }

  Future<void> _createDemoAccount() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final accountId = await DatabaseService.instance.accountRepository.createAccount(
        firstname: 'John',
        lastname: 'Doe',
        email: '<EMAIL>',
        password: 'password123',
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Demo account created with ID: $accountId')),
        );
      }

      await _loadDatabaseStats();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error creating demo account: $e')),
        );
      }
    }
  }

  Future<void> _createDemoRealm() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final realmId = await DatabaseService.instance.realmRepository.createRealm(
        name: 'Sample Real Estate Company',
        isCompany: true,
        companyName: 'Sample Real Estate LLC',
        companyLegalStructure: 'LLC',
        locale: 'en',
        currency: 'USD',
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Demo realm created with ID: $realmId')),
        );
      }

      await _loadDatabaseStats();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error creating demo realm: $e')),
        );
      }
    }
  }

  Future<void> _performHealthCheck() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final isHealthy = await DatabaseService.instance.healthCheck();
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isHealthy ? 'Database is healthy!' : 'Database health check failed!'),
            backgroundColor: isHealthy ? Colors.green : Colors.red,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Health check error: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: const Text('MicroRealEstate'),
        actions: [
          const ThemeToggleButton(),
          IconButton(
            icon: const Icon(Icons.palette),
            onPressed: () => AppRoutesWidget.goToThemeCustomizer(context),
            tooltip: 'Customize Appearance',
          ),
          IconButton(
            icon: const Icon(Icons.menu),
            onPressed: () => AppNavigationHelper.showNavigationDrawer(context),
            tooltip: 'Navigation Menu',
          ),
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => AppNavigationHelper.showQuickNavigation(context),
            tooltip: 'Quick Navigation',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Welcome Card
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.home_work,
                                size: 32,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Welcome to MicroRealEstate',
                                      style: Theme.of(context).textTheme.headlineSmall,
                                    ),
                                    Text(
                                      'Your comprehensive real estate management solution',
                                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Quick Actions Card
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Quick Actions',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          const SizedBox(height: 16),
                          GridView.count(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            crossAxisCount: MediaQuery.of(context).size.width > 600 ? 4 : 2,
                            crossAxisSpacing: 12,
                            mainAxisSpacing: 12,
                            childAspectRatio: 1.2,
                            children: [
                              _buildQuickActionCard(
                                context,
                                'Dashboard',
                                Icons.dashboard,
                                Colors.blue,
                                () => AppRoutesWidget.goToDashboard(context),
                              ),
                              _buildQuickActionCard(
                                context,
                                'Properties',
                                Icons.home,
                                Colors.green,
                                () => AppRoutesWidget.navigateTo(context, AppRoutesWidget.properties),
                              ),
                              _buildQuickActionCard(
                                context,
                                'Tenants',
                                Icons.people,
                                Colors.orange,
                                () => AppRoutesWidget.navigateTo(context, AppRoutesWidget.tenants),
                              ),
                              _buildQuickActionCard(
                                context,
                                'Leases',
                                Icons.description,
                                Colors.purple,
                                () => AppRoutesWidget.navigateTo(context, AppRoutesWidget.leases),
                              ),
                              _buildQuickActionCard(
                                context,
                                'Documents',
                                Icons.folder,
                                Colors.teal,
                                () => AppRoutesWidget.goToDocuments(context),
                              ),
                              _buildQuickActionCard(
                                context,
                                'Reports',
                                Icons.analytics,
                                Colors.indigo,
                                () => AppRoutesWidget.goToReports(context),
                              ),
                              _buildQuickActionCard(
                                context,
                                'Settings',
                                Icons.settings,
                                Colors.grey,
                                () => AppRoutesWidget.goToSettings(context),
                              ),
                              _buildQuickActionCard(
                                context,
                                'Framework Demo',
                                Icons.web,
                                Colors.red,
                                () => AppRoutesWidget.navigateTo(context, AppRoutesWidget.frameworkDemo),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Database Statistics Card
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                'Database Statistics',
                                style: Theme.of(context).textTheme.titleLarge,
                              ),
                              const Spacer(),
                              IconButton(
                                onPressed: _loadDatabaseStats,
                                icon: const Icon(Icons.refresh),
                                tooltip: 'Refresh Statistics',
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          if (_databaseStats != null) ...[
                            _buildStatRow('Platform', _databaseStats!['platform'] ?? 'Unknown'),
                            _buildStatRow('Total Accounts', _databaseStats!['accounts']['totalAccounts']?.toString() ?? '0'),
                            _buildStatRow('Total Realms', _databaseStats!['totalRealms']?.toString() ?? '0'),
                            _buildStatRow('Total Properties', _databaseStats!['totalProperties']?.toString() ?? '0'),
                            _buildStatRow('Total Leases', _databaseStats!['totalLeases']?.toString() ?? '0'),
                            _buildStatRow('Total Tenants', _databaseStats!['totalTenants']?.toString() ?? '0'),
                            _buildStatRow('Total Documents', _databaseStats!['totalDocuments']?.toString() ?? '0'),
                            _buildStatRow('Total Emails', _databaseStats!['totalEmails']?.toString() ?? '0'),
                            _buildStatRow('Total Templates', _databaseStats!['totalTemplates']?.toString() ?? '0'),
                          ] else
                            const Text('No statistics available'),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Database Actions Card
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Database Actions',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          const SizedBox(height: 16),
                          Wrap(
                            spacing: 8.0,
                            runSpacing: 8.0,
                            children: [
                              ElevatedButton(
                                onPressed: _createDemoAccount,
                                child: const Text('Create Demo Account'),
                              ),
                              ElevatedButton(
                                onPressed: _createDemoRealm,
                                child: const Text('Create Demo Realm'),
                              ),
                              ElevatedButton(
                                onPressed: _performHealthCheck,
                                child: const Text('Health Check'),
                              ),
                              ElevatedButton.icon(
                                onPressed: () => AppRoutesWidget.navigateTo(context, AppRoutesWidget.databaseViewer),
                                icon: const Icon(Icons.storage),
                                label: const Text('DB Viewer'),
                              ),
                              ElevatedButton.icon(
                                onPressed: () => AppRoutesWidget.navigateTo(context, AppRoutesWidget.databaseInspector),
                                icon: const Icon(Icons.search),
                                label: const Text('DB Inspector'),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Theme Settings Card
                  const ThemeSettingsCard(),
                  const SizedBox(height: 16),

                  // Database Viewer Widget
                  const DatabaseViewerWidget(),
                  const SizedBox(height: 16),

                  // Platform Information Card
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Platform Information',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          const SizedBox(height: 16),
                          ...DatabaseService.instance.getPlatformInfo().entries.map(
                            (entry) => _buildStatRow(entry.key, entry.value.toString()),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildQuickActionCard(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 32,
                color: color,
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontWeight: FontWeight.w500)),
          Text(value),
        ],
      ),
    );
  }
}
