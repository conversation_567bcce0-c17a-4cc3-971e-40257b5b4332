import 'package:flutter/material.dart';
import 'package:flex_color_picker/flex_color_picker.dart';
import 'thememanager.dart';
import 'themeconfig.dart';
import 'claytheme.dart';
import '../dropdownselect.dart';
import '../clay/claywidgets.dart';

/// Streamlined appearance customization widget
class AppearanceCustomizer extends StatefulWidget {
  const AppearanceCustomizer({super.key});

  @override
  State<AppearanceCustomizer> createState() => _AppearanceCustomizerState();
}

class _AppearanceCustomizerState extends State<AppearanceCustomizer> {
  late ThemeManager _themeManager;
  bool _isSelectingPrimaryColor = true; // Toggle between primary and secondary color selection

  @override
  void initState() {
    super.initState();
    _themeManager = ThemeManager.instance;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Appearance'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => _showResetDialog(),
            tooltip: 'Reset to Default',
          ),
        ],
      ),
      body: ListenableBuilder(
        listenable: _themeManager,
        builder: (context, child) {
          return ListView(
            padding: const EdgeInsets.all(ThemeConfig.spacingM),
            children: [
              _buildColorSection(),
              const SizedBox(height: ThemeConfig.spacingL),
              _buildThemeModeSection(),
              const SizedBox(height: ThemeConfig.spacingL),
              _buildStyleSection(),
              const SizedBox(height: ThemeConfig.spacingL),
              _buildAccessibilitySection(),
            ],
          );
        },
      ),
    );
  }

  Widget _buildColorSection() {
    return ClayCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Colors',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: ThemeConfig.spacingM),

            // Color selection toggle
            Row(
              children: [
                Expanded(
                  child: SegmentedButton<bool>(
                    segments: const [
                      ButtonSegment<bool>(
                        value: true,
                        label: Text('Primary Color'),
                        icon: Icon(Icons.palette),
                      ),
                      ButtonSegment<bool>(
                        value: false,
                        label: Text('Secondary Color'),
                        icon: Icon(Icons.color_lens),
                      ),
                    ],
                    selected: {_isSelectingPrimaryColor},
                    onSelectionChanged: (Set<bool> selection) {
                      setState(() {
                        _isSelectingPrimaryColor = selection.first;
                      });
                    },
                  ),
                ),
              ],
            ),

            const SizedBox(height: ThemeConfig.spacingM),

            // Color picker
            ColorPicker(
              color: _isSelectingPrimaryColor
                  ? _themeManager.primaryColor
                  : _themeManager.secondaryColor,
              onColorChanged: (color) {
                if (_isSelectingPrimaryColor) {
                  _themeManager.setPrimaryColor(color);
                } else {
                  _themeManager.setSecondaryColor(color);
                }
              },
              width: 40,
              height: 40,
              borderRadius: ThemeConfig.radiusM,
              spacing: ThemeConfig.spacingS,
              runSpacing: ThemeConfig.spacingS,
              wheelDiameter: 200,
              heading: Text(
                _isSelectingPrimaryColor ? 'Select Primary Color' : 'Select Secondary Color',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              subheading: Text(
                _isSelectingPrimaryColor
                    ? 'Choose your main brand color'
                    : 'Choose your accent color',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              wheelSubheading: Text(
                'Color Wheel',
                style: Theme.of(context).textTheme.titleSmall,
              ),
              showMaterialName: true,
              showColorName: true,
              showColorCode: true,
              copyPasteBehavior: const ColorPickerCopyPasteBehavior(
                longPressMenu: true,
              ),
              materialNameTextStyle: Theme.of(context).textTheme.bodySmall,
              colorNameTextStyle: Theme.of(context).textTheme.bodySmall,
              colorCodeTextStyle: Theme.of(context).textTheme.bodySmall,
              pickersEnabled: const <ColorPickerType, bool>{
                ColorPickerType.both: false,
                ColorPickerType.primary: true,
                ColorPickerType.accent: false,
                ColorPickerType.bw: false,
                ColorPickerType.custom: true,
                ColorPickerType.wheel: true,
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildThemeModeSection() {
    return ClayCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Theme Mode',
            style: Theme.of(context).textTheme.titleLarge,
          ),
            const SizedBox(height: ThemeConfig.spacingM),

            DropdownSelectFactory.themeMode(
              value: _themeManager.themeMode,
              onChanged: (mode) {
                if (mode != null) {
                  _themeManager.setThemeMode(mode);
                }
              },
            ),

            const SizedBox(height: ThemeConfig.spacingM),

            ListTile(
              leading: Icon(
                _themeManager.isDarkMode(context)
                    ? Icons.dark_mode
                    : Icons.light_mode,
              ),
              title: Text(
                'Current: ${_themeManager.isDarkMode(context) ? 'Dark' : 'Light'}',
              ),
              subtitle: Text(
                'Mode: ${_themeManager.getThemeModeDisplayName()}',
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStyleSection() {
    return ClayCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Style',
            style: Theme.of(context).textTheme.titleLarge,
          ),
            const SizedBox(height: ThemeConfig.spacingM),

            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Material 3',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      Text(
                        'Use Material You design system',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ),
                ),
                ClaySwitch(
                  value: _themeManager.useMaterial3,
                  onChanged: (value) {
                    _themeManager.setUseMaterial3(value);
                  },
                ),
              ],
            ),

            const SizedBox(height: ThemeConfig.spacingM),

            DropdownSelectFactory.fontFamily(
              value: _themeManager.fontFamily,
              onChanged: (value) {
                if (value != null) {
                  _themeManager.setFontFamily(value);
                }
              },
              fontFamilies: ThemeConfig.availableFontFamilies,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccessibilitySection() {
    return ClayCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Accessibility',
            style: Theme.of(context).textTheme.titleLarge,
          ),
            const SizedBox(height: ThemeConfig.spacingM),

            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'High Contrast Mode',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      Text(
                        'Improves visibility for users with visual impairments',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ),
                ),
                ClaySwitch(
                  value: _themeManager.highContrastMode,
                  onChanged: (value) {
                    _themeManager.setHighContrastMode(value);
                  },
                ),
              ],
            ),

            const SizedBox(height: ThemeConfig.spacingM),

            Text(
              'Text Scale Factor: ${_themeManager.textScaleFactor.toStringAsFixed(1)}x',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: ThemeConfig.spacingS),

            ClaySlider(
              value: _themeManager.textScaleFactor,
              min: 0.8,
              max: 2.0,
              divisions: 12,
              label: '${_themeManager.textScaleFactor.toStringAsFixed(1)}x',
              onChanged: (value) {
                _themeManager.setTextScaleFactor(value);
              },
            ),

            Text(
              'Adjust text size for better readability',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }

  void _showResetDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Appearance'),
        content: const Text('Are you sure you want to reset all appearance settings to default?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              _themeManager.resetToDefault();
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Appearance reset to default')),
              );
            },
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }
}


