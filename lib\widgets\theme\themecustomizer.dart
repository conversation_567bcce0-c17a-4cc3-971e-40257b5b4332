import 'package:flutter/material.dart';
import 'package:flex_color_picker/flex_color_picker.dart';
import 'thememanager.dart';
import 'themeconfig.dart';

/// Streamlined appearance customization widget
class AppearanceCustomizer extends StatefulWidget {
  const AppearanceCustomizer({super.key});

  @override
  State<AppearanceCustomizer> createState() => _AppearanceCustomizerState();
}

class _AppearanceCustomizerState extends State<AppearanceCustomizer> {
  late ThemeManager _themeManager;
  bool _isSelectingPrimaryColor = true; // Toggle between primary and secondary color selection

  @override
  void initState() {
    super.initState();
    _themeManager = ThemeManager.instance;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Appearance'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => _showResetDialog(),
            tooltip: 'Reset to Default',
          ),
        ],
      ),
      body: ListenableBuilder(
        listenable: _themeManager,
        builder: (context, child) {
          return ListView(
            padding: const EdgeInsets.all(ThemeConfig.spacingM),
            children: [
              _buildColorSection(),
              const SizedBox(height: ThemeConfig.spacingL),
              _buildThemeModeSection(),
              const SizedBox(height: ThemeConfig.spacingL),
              _buildStyleSection(),
              const SizedBox(height: ThemeConfig.spacingL),
              _buildAccessibilitySection(),
            ],
          );
        },
      ),
    );
  }

  Widget _buildColorSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(ThemeConfig.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Colors',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: ThemeConfig.spacingM),

            // Color selection toggle
            Row(
              children: [
                Expanded(
                  child: SegmentedButton<bool>(
                    segments: const [
                      ButtonSegment<bool>(
                        value: true,
                        label: Text('Primary Color'),
                        icon: Icon(Icons.palette),
                      ),
                      ButtonSegment<bool>(
                        value: false,
                        label: Text('Secondary Color'),
                        icon: Icon(Icons.color_lens),
                      ),
                    ],
                    selected: {_isSelectingPrimaryColor},
                    onSelectionChanged: (Set<bool> selection) {
                      setState(() {
                        _isSelectingPrimaryColor = selection.first;
                      });
                    },
                  ),
                ),
              ],
            ),

            const SizedBox(height: ThemeConfig.spacingM),

            // Color picker
            ColorPicker(
              color: _isSelectingPrimaryColor
                  ? _themeManager.primaryColor
                  : _themeManager.secondaryColor,
              onColorChanged: (color) {
                if (_isSelectingPrimaryColor) {
                  _themeManager.setPrimaryColor(color);
                } else {
                  _themeManager.setSecondaryColor(color);
                }
              },
              width: 40,
              height: 40,
              borderRadius: ThemeConfig.radiusM,
              spacing: ThemeConfig.spacingS,
              runSpacing: ThemeConfig.spacingS,
              wheelDiameter: 200,
              heading: Text(
                _isSelectingPrimaryColor ? 'Select Primary Color' : 'Select Secondary Color',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              subheading: Text(
                _isSelectingPrimaryColor
                    ? 'Choose your main brand color'
                    : 'Choose your accent color',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              wheelSubheading: Text(
                'Color Wheel',
                style: Theme.of(context).textTheme.titleSmall,
              ),
              showMaterialName: true,
              showColorName: true,
              showColorCode: true,
              copyPasteBehavior: const ColorPickerCopyPasteBehavior(
                longPressMenu: true,
              ),
              materialNameTextStyle: Theme.of(context).textTheme.bodySmall,
              colorNameTextStyle: Theme.of(context).textTheme.bodySmall,
              colorCodeTextStyle: Theme.of(context).textTheme.bodySmall,
              pickersEnabled: const <ColorPickerType, bool>{
                ColorPickerType.both: false,
                ColorPickerType.primary: true,
                ColorPickerType.accent: false,
                ColorPickerType.bw: false,
                ColorPickerType.custom: true,
                ColorPickerType.wheel: true,
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildThemeModeSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(ThemeConfig.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Theme Mode',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: ThemeConfig.spacingM),

            _ThemeModeDropdown(
              currentMode: _themeManager.themeMode,
              onChanged: (mode) {
                if (mode != null) {
                  _themeManager.setThemeMode(mode);
                }
              },
            ),

            const SizedBox(height: ThemeConfig.spacingM),

            ListTile(
              leading: Icon(
                _themeManager.isDarkMode(context)
                    ? Icons.dark_mode
                    : Icons.light_mode,
              ),
              title: Text(
                'Current: ${_themeManager.isDarkMode(context) ? 'Dark' : 'Light'}',
              ),
              subtitle: Text(
                'Mode: ${_themeManager.getThemeModeDisplayName()}',
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStyleSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(ThemeConfig.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Style',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: ThemeConfig.spacingM),

            SwitchListTile(
              title: const Text('Material 3'),
              subtitle: const Text('Use Material You design system'),
              value: _themeManager.useMaterial3,
              onChanged: (value) {
                _themeManager.setUseMaterial3(value);
              },
            ),

            const SizedBox(height: ThemeConfig.spacingM),

            DropdownButtonFormField<String>(
              value: _themeManager.fontFamily,
              decoration: const InputDecoration(
                labelText: 'Font Family',
                border: OutlineInputBorder(),
              ),
              items: ThemeConfig.availableFontFamilies.map((fontFamily) {
                return DropdownMenuItem(
                  value: fontFamily,
                  child: Text(fontFamily == 'System' ? 'System Default' : fontFamily),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  _themeManager.setFontFamily(value);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccessibilitySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(ThemeConfig.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Accessibility',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: ThemeConfig.spacingM),

            SwitchListTile(
              title: const Text('High Contrast Mode'),
              subtitle: const Text('Improves visibility for users with visual impairments'),
              value: _themeManager.highContrastMode,
              onChanged: (value) {
                _themeManager.setHighContrastMode(value);
              },
            ),

            const SizedBox(height: ThemeConfig.spacingM),

            Text(
              'Text Scale Factor: ${_themeManager.textScaleFactor.toStringAsFixed(1)}x',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: ThemeConfig.spacingS),

            Slider(
              value: _themeManager.textScaleFactor,
              min: 0.8,
              max: 2.0,
              divisions: 12,
              label: '${_themeManager.textScaleFactor.toStringAsFixed(1)}x',
              onChanged: (value) {
                _themeManager.setTextScaleFactor(value);
              },
            ),

            Text(
              'Adjust text size for better readability',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }

  void _showResetDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Appearance'),
        content: const Text('Are you sure you want to reset all appearance settings to default?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              _themeManager.resetToDefault();
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Appearance reset to default')),
              );
            },
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }
}

/// Custom dropdown widget for theme mode selection
class _ThemeModeDropdown extends StatelessWidget {
  final ThemeMode currentMode;
  final ValueChanged<ThemeMode?> onChanged;

  const _ThemeModeDropdown({
    required this.currentMode,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<ThemeMode>(
      value: currentMode,
      decoration: const InputDecoration(
        labelText: 'Theme Mode',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.brightness_6),
      ),
      items: const [
        DropdownMenuItem(
          value: ThemeMode.system,
          child: Row(
            children: [
              Icon(Icons.settings_suggest),
              SizedBox(width: 8),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text('System'),
                  Text(
                    'Follow system settings',
                    style: TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                ],
              ),
            ],
          ),
        ),
        DropdownMenuItem(
          value: ThemeMode.light,
          child: Row(
            children: [
              Icon(Icons.light_mode),
              SizedBox(width: 8),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text('Light'),
                  Text(
                    'Always use light theme',
                    style: TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                ],
              ),
            ],
          ),
        ),
        DropdownMenuItem(
          value: ThemeMode.dark,
          child: Row(
            children: [
              Icon(Icons.dark_mode),
              SizedBox(width: 8),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text('Dark'),
                  Text(
                    'Always use dark theme',
                    style: TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
      onChanged: onChanged,
    );
  }
}


