import 'package:flutter/material.dart';
import 'package:clay_containers/clay_containers.dart' hide ClayTheme;
import '../theme/claytheme.dart';

/// Clay-themed button widget
class ClayButton extends StatefulWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double borderRadius;
  final Color? color;
  final ClayThemeSurface surface;
  final bool enabled;

  const ClayButton({
    super.key,
    required this.child,
    this.onPressed,
    this.width,
    this.height,
    this.padding = const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
    this.margin,
    this.borderRadius = ClayTheme.radiusMedium,
    this.color,
    this.surface = ClayThemeSurface.surface,
    this.enabled = true,
  });

  @override
  State<ClayButton> createState() => _ClayButtonState();
}

class _ClayButtonState extends State<ClayButton> {
  bool _isPressed = false;

  @override
  Widget build(BuildContext context) {
    final isEnabled = widget.enabled && widget.onPressed != null;
    
    return GestureDetector(
      onTapDown: isEnabled ? (_) => setState(() => _isPressed = true) : null,
      onTapUp: isEnabled ? (_) => setState(() => _isPressed = false) : null,
      onTapCancel: isEnabled ? () => setState(() => _isPressed = false) : null,
      onTap: isEnabled ? widget.onPressed : null,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 150),
        child: _isPressed
            ? ClayTheme.createPressedContainer(
                context: context,
                width: widget.width,
                height: widget.height,
                padding: widget.padding,
                margin: widget.margin,
                borderRadius: widget.borderRadius,
                color: widget.color,
                surface: widget.surface,
                child: widget.child,
              )
            : ClayTheme.createContainer(
                context: context,
                width: widget.width,
                height: widget.height,
                padding: widget.padding,
                margin: widget.margin,
                borderRadius: widget.borderRadius,
                color: widget.color,
                surface: widget.surface,
                child: widget.child,
              ),
      ),
    );
  }
}

/// Clay-themed card widget
class ClayCard extends StatelessWidget {
  final Widget child;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double borderRadius;
  final Color? color;
  final ClayThemeSurface surface;
  final VoidCallback? onTap;
  final double depth;
  final double spread;

  const ClayCard({
    super.key,
    required this.child,
    this.width,
    this.height,
    this.padding = const EdgeInsets.all(16),
    this.margin,
    this.borderRadius = ClayTheme.radiusMedium,
    this.color,
    this.surface = ClayThemeSurface.surface,
    this.onTap,
    this.depth = ClayTheme.depthMedium,
    this.spread = ClayTheme.spreadNormal,
  });

  @override
  Widget build(BuildContext context) {
    return ClayTheme.createContainer(
      context: context,
      width: width,
      height: height,
      padding: padding,
      margin: margin,
      depth: depth,
      spread: spread,
      borderRadius: borderRadius,
      color: color,
      surface: surface,
      onTap: onTap,
      child: child,
    );
  }
}

/// Clay-themed floating action button
class ClayFloatingActionButton extends StatefulWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final double size;
  final Color? color;
  final ClayThemeSurface surface;

  const ClayFloatingActionButton({
    super.key,
    required this.child,
    this.onPressed,
    this.size = 56,
    this.color,
    this.surface = ClayThemeSurface.elevated,
  });

  @override
  State<ClayFloatingActionButton> createState() => _ClayFloatingActionButtonState();
}

class _ClayFloatingActionButtonState extends State<ClayFloatingActionButton> {
  bool _isPressed = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: widget.onPressed != null ? (_) => setState(() => _isPressed = true) : null,
      onTapUp: widget.onPressed != null ? (_) => setState(() => _isPressed = false) : null,
      onTapCancel: widget.onPressed != null ? () => setState(() => _isPressed = false) : null,
      onTap: widget.onPressed,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 150),
        child: _isPressed
            ? ClayTheme.createPressedContainer(
                context: context,
                width: widget.size,
                height: widget.size,
                borderRadius: ClayTheme.radiusCircular,
                color: widget.color,
                surface: widget.surface,
                child: Center(child: widget.child),
              )
            : ClayTheme.createFloatingContainer(
                context: context,
                width: widget.size,
                height: widget.size,
                borderRadius: ClayTheme.radiusCircular,
                color: widget.color,
                surface: widget.surface,
                child: Center(child: widget.child),
              ),
      ),
    );
  }
}

/// Clay-themed text field
class ClayTextField extends StatelessWidget {
  final TextEditingController? controller;
  final String? labelText;
  final String? hintText;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final bool obscureText;
  final TextInputType? keyboardType;
  final ValueChanged<String>? onChanged;
  final VoidCallback? onTap;
  final bool readOnly;
  final int? maxLines;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double borderRadius;
  final Color? color;
  final ClayThemeSurface surface;

  const ClayTextField({
    super.key,
    this.controller,
    this.labelText,
    this.hintText,
    this.prefixIcon,
    this.suffixIcon,
    this.obscureText = false,
    this.keyboardType,
    this.onChanged,
    this.onTap,
    this.readOnly = false,
    this.maxLines = 1,
    this.padding = const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
    this.margin,
    this.borderRadius = ClayTheme.radiusMedium,
    this.color,
    this.surface = ClayThemeSurface.surfaceVariant,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return ClayTheme.createPressedContainer(
      context: context,
      padding: padding,
      margin: margin,
      borderRadius: borderRadius,
      color: color,
      surface: surface,
      child: TextField(
        controller: controller,
        obscureText: obscureText,
        keyboardType: keyboardType,
        onChanged: onChanged,
        onTap: onTap,
        readOnly: readOnly,
        maxLines: maxLines,
        style: theme.textTheme.bodyLarge,
        decoration: InputDecoration(
          labelText: labelText,
          hintText: hintText,
          prefixIcon: prefixIcon,
          suffixIcon: suffixIcon,
          border: InputBorder.none,
          contentPadding: EdgeInsets.zero,
        ),
      ),
    );
  }
}

/// Clay-themed switch
class ClaySwitch extends StatelessWidget {
  final bool value;
  final ValueChanged<bool>? onChanged;
  final Color? activeColor;
  final Color? inactiveColor;
  final double size;

  const ClaySwitch({
    super.key,
    required this.value,
    this.onChanged,
    this.activeColor,
    this.inactiveColor,
    this.size = 50,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveActiveColor = activeColor ?? theme.colorScheme.primary;
    final effectiveInactiveColor = inactiveColor ?? ClayTheme.getClayColor(context);

    return GestureDetector(
      onTap: onChanged != null ? () => onChanged!(!value) : null,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: size * 1.8,
        height: size,
        child: ClayTheme.createPressedContainer(
          context: context,
          borderRadius: ClayTheme.radiusCircular,
          color: value ? effectiveActiveColor : effectiveInactiveColor,
          surface: ClayThemeSurface.surface,
          child: AnimatedAlign(
            duration: const Duration(milliseconds: 200),
            alignment: value ? Alignment.centerRight : Alignment.centerLeft,
            child: Container(
              margin: const EdgeInsets.all(4),
              child: ClayTheme.createContainer(
                context: context,
                width: size * 0.8,
                height: size * 0.8,
                borderRadius: ClayTheme.radiusCircular,
                depth: ClayTheme.depthSubtle,
                child: const SizedBox(),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// Clay-themed slider
class ClaySlider extends StatelessWidget {
  final double value;
  final ValueChanged<double>? onChanged;
  final double min;
  final double max;
  final int? divisions;
  final String? label;
  final Color? activeColor;
  final Color? inactiveColor;
  final double height;
  final EdgeInsetsGeometry? margin;

  const ClaySlider({
    super.key,
    required this.value,
    this.onChanged,
    this.min = 0.0,
    this.max = 1.0,
    this.divisions,
    this.label,
    this.activeColor,
    this.inactiveColor,
    this.height = 40,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveActiveColor = activeColor ?? theme.colorScheme.primary;

    return Container(
      height: height,
      margin: margin,
      child: ClayTheme.createPressedContainer(
        context: context,
        borderRadius: ClayTheme.radiusCircular,
        surface: ClayThemeSurface.surfaceVariant,
        child: SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: effectiveActiveColor,
            inactiveTrackColor: Colors.transparent,
            thumbColor: effectiveActiveColor,
            overlayColor: effectiveActiveColor.withOpacity(0.2),
            trackHeight: 4,
            thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
          ),
          child: Slider(
            value: value,
            onChanged: onChanged,
            min: min,
            max: max,
            divisions: divisions,
            label: label,
          ),
        ),
      ),
    );
  }
}
