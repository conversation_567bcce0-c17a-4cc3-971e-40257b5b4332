import 'package:flutter/material.dart';
import 'package:clay_containers/clay_containers.dart';

/// Clay theme configuration for neumorphic design
class ClayTheme {
  ClayTheme._();

  // Clay container depths
  static const double depthFlat = 0;
  static const double depthSubtle = 20;
  static const double depthMedium = 40;
  static const double depthDeep = 60;
  static const double depthPressed = -20;

  // Clay container spreads
  static const double spreadTight = 1;
  static const double spreadNormal = 2;
  static const double spreadWide = 4;

  // Clay border radius
  static const double radiusSmall = 8;
  static const double radiusMedium = 12;
  static const double radiusLarge = 16;
  static const double radiusXLarge = 24;
  static const double radiusCircular = 50;

  /// Get clay color based on theme brightness
  static Color getClayColor(BuildContext context, {Color? customColor}) {
    if (customColor != null) return customColor;
    
    final theme = Theme.of(context);
    final brightness = theme.brightness;
    
    if (brightness == Brightness.dark) {
      return const Color(0xFF2E2E2E); // Dark clay color
    } else {
      return const Color(0xFFE0E5EC); // Light clay color
    }
  }

  /// Get clay color for different surface levels
  static Color getClayColorForSurface(BuildContext context, {
    required ClayThemeSurface surface,
    Color? customColor,
  }) {
    if (customColor != null) return customColor;
    
    final theme = Theme.of(context);
    final brightness = theme.brightness;
    
    if (brightness == Brightness.dark) {
      switch (surface) {
        case ClayThemeSurface.background:
          return const Color(0xFF1A1A1A);
        case ClayThemeSurface.surface:
          return const Color(0xFF2E2E2E);
        case ClayThemeSurface.surfaceVariant:
          return const Color(0xFF3A3A3A);
        case ClayThemeSurface.elevated:
          return const Color(0xFF404040);
      }
    } else {
      switch (surface) {
        case ClayThemeSurface.background:
          return const Color(0xFFE0E5EC);
        case ClayThemeSurface.surface:
          return const Color(0xFFE0E5EC);
        case ClayThemeSurface.surfaceVariant:
          return const Color(0xFFD1D9E6);
        case ClayThemeSurface.elevated:
          return const Color(0xFFEBF0F7);
      }
    }
  }

  /// Create a standard clay container configuration
  static ClayContainer createContainer({
    required BuildContext context,
    required Widget child,
    double? width,
    double? height,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double depth = depthMedium,
    double spread = spreadNormal,
    double borderRadius = radiusMedium,
    Color? color,
    ClayThemeSurface surface = ClayThemeSurface.surface,
    VoidCallback? onTap,
  }) {
    return ClayContainer(
      width: width,
      height: height,
      depth: depth,
      spread: spread,
      borderRadius: borderRadius,
      color: color ?? getClayColorForSurface(context, surface: surface),
      parentColor: color ?? getClayColorForSurface(context, surface: surface),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(borderRadius),
          child: Container(
            padding: padding,
            margin: margin,
            child: child,
          ),
        ),
      ),
    );
  }

  /// Create a pressed clay container (inset effect)
  static ClayContainer createPressedContainer({
    required BuildContext context,
    required Widget child,
    double? width,
    double? height,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double depth = depthPressed,
    double spread = spreadTight,
    double borderRadius = radiusMedium,
    Color? color,
    ClayThemeSurface surface = ClayThemeSurface.surface,
  }) {
    return ClayContainer(
      width: width,
      height: height,
      depth: depth,
      spread: spread,
      borderRadius: borderRadius,
      color: color ?? getClayColorForSurface(context, surface: surface),
      parentColor: color ?? getClayColorForSurface(context, surface: surface),
      child: Container(
        padding: padding,
        margin: margin,
        child: child,
      ),
    );
  }

  /// Create a floating clay container (elevated effect)
  static ClayContainer createFloatingContainer({
    required BuildContext context,
    required Widget child,
    double? width,
    double? height,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double depth = depthDeep,
    double spread = spreadWide,
    double borderRadius = radiusLarge,
    Color? color,
    ClayThemeSurface surface = ClayThemeSurface.elevated,
    VoidCallback? onTap,
  }) {
    return ClayContainer(
      width: width,
      height: height,
      depth: depth,
      spread: spread,
      borderRadius: borderRadius,
      color: color ?? getClayColorForSurface(context, surface: surface),
      parentColor: color ?? getClayColorForSurface(context, surface: surface),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(borderRadius),
          child: Container(
            padding: padding,
            margin: margin,
            child: child,
          ),
        ),
      ),
    );
  }

  /// Create a subtle clay container (minimal depth)
  static ClayContainer createSubtleContainer({
    required BuildContext context,
    required Widget child,
    double? width,
    double? height,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double depth = depthSubtle,
    double spread = spreadTight,
    double borderRadius = radiusSmall,
    Color? color,
    ClayThemeSurface surface = ClayThemeSurface.surfaceVariant,
    VoidCallback? onTap,
  }) {
    return ClayContainer(
      width: width,
      height: height,
      depth: depth,
      spread: spread,
      borderRadius: borderRadius,
      color: color ?? getClayColorForSurface(context, surface: surface),
      parentColor: color ?? getClayColorForSurface(context, surface: surface),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(borderRadius),
          child: Container(
            padding: padding,
            margin: margin,
            child: child,
          ),
        ),
      ),
    );
  }

  /// Create a flat clay container (no depth)
  static ClayContainer createFlatContainer({
    required BuildContext context,
    required Widget child,
    double? width,
    double? height,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double depth = depthFlat,
    double spread = spreadTight,
    double borderRadius = radiusSmall,
    Color? color,
    ClayThemeSurface surface = ClayThemeSurface.surface,
    VoidCallback? onTap,
  }) {
    return ClayContainer(
      width: width,
      height: height,
      depth: depth,
      spread: spread,
      borderRadius: borderRadius,
      color: color ?? getClayColorForSurface(context, surface: surface),
      parentColor: color ?? getClayColorForSurface(context, surface: surface),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(borderRadius),
          child: Container(
            padding: padding,
            margin: margin,
            child: child,
          ),
        ),
      ),
    );
  }
}

/// Surface types for clay theming
enum ClayThemeSurface {
  background,
  surface,
  surfaceVariant,
  elevated,
}
