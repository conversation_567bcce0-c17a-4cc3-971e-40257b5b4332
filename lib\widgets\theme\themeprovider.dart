import 'package:flutter/material.dart';
import 'thememanager.dart';
import 'themeconfig.dart';
import 'themecustomizer.dart';

/// Theme provider widget that provides theme management without MaterialApp
/// This allows the main app to control routing and other MaterialApp properties
class ThemeProvider extends StatefulWidget {
  final Widget child;

  const ThemeProvider({
    super.key,
    required this.child,
  });

  @override
  State<ThemeProvider> createState() => _ThemeProviderState();
}

class _ThemeProviderState extends State<ThemeProvider> {
  late ThemeManager _themeManager;

  @override
  void initState() {
    super.initState();
    _themeManager = ThemeManager.instance;
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: _themeManager,
      builder: (context, child) {
        // Just provide the child without wrapping in MaterialApp
        // The main app will handle MaterialApp with proper theme data
        return widget.child;
      },
    );
  }
}

/// Extension to easily access theme manager from context
extension ThemeContext on BuildContext {
  ThemeManager get themeManager => ThemeManager.instance;

  bool get isDarkMode => themeManager.isDarkMode(this);

  ColorScheme get colorScheme => Theme.of(this).colorScheme;

  TextTheme get textTheme => Theme.of(this).textTheme;

  /// Get responsive spacing for current screen size
  double get responsiveSpacing => themeManager.getResponsiveSpacing(this);

  /// Get device type for current screen size
  DeviceType get deviceType => themeManager.getDeviceType(this);

  /// Check if current screen is mobile
  bool get isMobile => deviceType == DeviceType.mobile;

  /// Check if current screen is tablet
  bool get isTablet => deviceType == DeviceType.tablet;

  /// Check if current screen is desktop
  bool get isDesktop => deviceType == DeviceType.desktop;

  /// Get screen width
  double get screenWidth => MediaQuery.of(this).size.width;

  /// Get screen height
  double get screenHeight => MediaQuery.of(this).size.height;

  /// Check if theme meets accessibility standards
  bool get meetsAccessibilityStandards => themeManager.meetsAccessibilityStandards(this);
}

/// Theme-aware widget that rebuilds when theme changes
class ThemeAware extends StatelessWidget {
  final Widget Function(BuildContext context, ThemeData theme) builder;
  
  const ThemeAware({
    super.key,
    required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: ThemeManager.instance,
      builder: (context, child) {
        return builder(context, Theme.of(context));
      },
    );
  }
}

/// Animated theme transition widget
class AnimatedThemeTransition extends StatelessWidget {
  final Widget child;
  final Duration duration;
  
  const AnimatedThemeTransition({
    super.key,
    required this.child,
    this.duration = const Duration(milliseconds: 300),
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedTheme(
      data: Theme.of(context),
      duration: duration,
      child: child,
    );
  }
}

/// Quick theme toggle button
class ThemeToggleButton extends StatelessWidget {
  final IconData? lightIcon;
  final IconData? darkIcon;
  final String? tooltip;
  
  const ThemeToggleButton({
    super.key,
    this.lightIcon = Icons.light_mode,
    this.darkIcon = Icons.dark_mode,
    this.tooltip,
  });

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: ThemeManager.instance,
      builder: (context, child) {
        final themeManager = ThemeManager.instance;
        final isDark = themeManager.isDarkMode(context);
        
        return IconButton(
          icon: Icon(isDark ? lightIcon : darkIcon),
          tooltip: tooltip ?? (isDark ? 'Switch to Light Mode' : 'Switch to Dark Mode'),
          onPressed: () {
            final newMode = isDark ? ThemeMode.light : ThemeMode.dark;
            themeManager.setThemeMode(newMode);
          },
        );
      },
    );
  }
}

/// Color scheme selector widget
class ColorSchemeSelector extends StatelessWidget {
  final bool showLabels;
  final double size;
  
  const ColorSchemeSelector({
    super.key,
    this.showLabels = true,
    this.size = 40,
  });

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: ThemeManager.instance,
      builder: (context, child) {
        final themeManager = ThemeManager.instance;
        
        return Wrap(
          spacing: 8,
          runSpacing: 8,
          children: ThemeConfig.colorSchemes.entries.map((entry) {
            final isSelected = themeManager.primaryColor == entry.value;
            
            return GestureDetector(
              onTap: () => themeManager.setPrimaryColor(entry.value),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: size,
                    height: size,
                    decoration: BoxDecoration(
                      color: entry.value,
                      shape: BoxShape.circle,
                      border: isSelected
                          ? Border.all(
                              color: Theme.of(context).colorScheme.primary,
                              width: 3,
                            )
                          : Border.all(
                              color: Colors.grey.withOpacity(0.3),
                              width: 1,
                            ),
                    ),
                    child: isSelected
                        ? Icon(
                            Icons.check,
                            color: entry.value.computeLuminance() > 0.5
                                ? Colors.black
                                : Colors.white,
                            size: size * 0.5,
                          )
                        : null,
                  ),
                  if (showLabels) ...[
                    const SizedBox(height: 4),
                    Text(
                      entry.key,
                      style: Theme.of(context).textTheme.bodySmall,
                      textAlign: TextAlign.center,
                    ),
                  ],
                ],
              ),
            );
          }).toList(),
        );
      },
    );
  }
}

/// Responsive widget that adapts to different screen sizes
class ResponsiveWidget extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;

  const ResponsiveWidget({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
  });

  @override
  Widget build(BuildContext context) {
    final deviceType = context.deviceType;

    switch (deviceType) {
      case DeviceType.mobile:
        return mobile;
      case DeviceType.tablet:
        return tablet ?? mobile;
      case DeviceType.desktop:
        return desktop ?? tablet ?? mobile;
    }
  }
}

/// Theme settings card widget
class ThemeSettingsCard extends StatelessWidget {
  const ThemeSettingsCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.palette),
                const SizedBox(width: 8),
                Text(
                  'Theme Settings',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const Spacer(),
                const ThemeToggleButton(),
              ],
            ),
            const SizedBox(height: 16),
            
            ListenableBuilder(
              listenable: ThemeManager.instance,
              builder: (context, child) {
                final themeManager = ThemeManager.instance;
                
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Current Theme: ${themeManager.getThemeModeDisplayName()}',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    const SizedBox(height: 8),
                    
                    Text(
                      'Primary Color',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    
                    const ColorSchemeSelector(
                      showLabels: false,
                      size: 32,
                    ),
                    
                    const SizedBox(height: 16),
                    
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed: () {
                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder: (context) => const ThemeCustomizer(),
                                ),
                              );
                            },
                            icon: const Icon(Icons.tune),
                            label: const Text('Customize'),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed: () => themeManager.resetToDefault(),
                            icon: const Icon(Icons.refresh),
                            label: const Text('Reset'),
                          ),
                        ),
                      ],
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}


