import 'package:flutter/material.dart';
import 'claytheme.dart';

/// Theme configuration class for MicroRealEstate app
class ThemeConfig {
  // Primary brand colors for real estate theme
  static const Color primaryBlue = Color(0xFF2196F3);
  static const Color primaryGreen = Color(0xFF4CAF50);
  static const Color primaryOrange = Color(0xFFFF9800);
  static const Color primaryRed = Color(0xFFF44336);
  static const Color primaryPurple = Color(0xFF9C27B0);
  static const Color primaryTeal = Color(0xFF009688);

  // Neutral colors
  static const Color darkGrey = Color(0xFF424242);
  static const Color mediumGrey = Color(0xFF757575);
  static const Color lightGrey = Color(0xFFE0E0E0);
  static const Color backgroundLight = Color(0xFFFAFAFA);
  static const Color backgroundDark = Color(0xFF121212);

  // Real estate specific colors
  static const Color propertyGreen = Color(0xFF2E7D32);
  static const Color tenantBlue = Color(0xFF1976D2);
  static const Color leaseOrange = Color(0xFFE65100);
  static const Color documentPurple = Color(0xFF7B1FA2);
  static const Color financialGold = Color(0xFFFF8F00);

  // Default theme colors
  static const Color defaultPrimary = primaryBlue;
  static const Color defaultSecondary = primaryGreen;
  static const Color defaultAccent = primaryOrange;

  // Theme mode options
  static const List<String> themeModes = ['System', 'Light', 'Dark'];
  
  // Color scheme options
  static const Map<String, Color> colorSchemes = {
    'Blue Ocean': primaryBlue,
    'Forest Green': primaryGreen,
    'Sunset Orange': primaryOrange,
    'Cherry Red': primaryRed,
    'Royal Purple': primaryPurple,
    'Teal Waters': primaryTeal,
    'Property Green': propertyGreen,
    'Tenant Blue': tenantBlue,
    'Lease Orange': leaseOrange,
    'Document Purple': documentPurple,
    'Financial Gold': financialGold,
  };

  // Typography scale
  static const TextTheme textTheme = TextTheme(
    displayLarge: TextStyle(fontSize: 32, fontWeight: FontWeight.bold),
    displayMedium: TextStyle(fontSize: 28, fontWeight: FontWeight.bold),
    displaySmall: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
    headlineLarge: TextStyle(fontSize: 22, fontWeight: FontWeight.w600),
    headlineMedium: TextStyle(fontSize: 20, fontWeight: FontWeight.w600),
    headlineSmall: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
    titleLarge: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
    titleMedium: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
    titleSmall: TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
    bodyLarge: TextStyle(fontSize: 16, fontWeight: FontWeight.normal),
    bodyMedium: TextStyle(fontSize: 14, fontWeight: FontWeight.normal),
    bodySmall: TextStyle(fontSize: 12, fontWeight: FontWeight.normal),
    labelLarge: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
    labelMedium: TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
    labelSmall: TextStyle(fontSize: 10, fontWeight: FontWeight.w500),
  );

  // Spacing constants
  static const double spacingXS = 4.0;
  static const double spacingS = 8.0;
  static const double spacingM = 16.0;
  static const double spacingL = 24.0;
  static const double spacingXL = 32.0;
  static const double spacingXXL = 48.0;

  // Border radius constants
  static const double radiusS = 4.0;
  static const double radiusM = 8.0;
  static const double radiusL = 12.0;
  static const double radiusXL = 16.0;
  static const double radiusRound = 50.0;

  // Elevation constants
  static const double elevationS = 2.0;
  static const double elevationM = 4.0;
  static const double elevationL = 8.0;
  static const double elevationXL = 16.0;

  // Animation durations
  static const Duration animationFast = Duration(milliseconds: 150);
  static const Duration animationMedium = Duration(milliseconds: 300);
  static const Duration animationSlow = Duration(milliseconds: 500);

  // Responsive breakpoints
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 900;
  static const double desktopBreakpoint = 1200;

  // Accessibility constants
  static const double minTouchTargetSize = 48.0;
  static const double accessibleTextScale = 1.2;
  static const double highContrastOpacity = 0.87;

  // Font families
  static const List<String> availableFontFamilies = [
    'System',
    'Roboto',
    'Open Sans',
    'Lato',
    'Montserrat',
    'Poppins',
    'Inter',
    'Source Sans Pro',
  ];

  // Clay theme integration
  static const bool useClayDesign = true;
  static const double clayDepthMultiplier = 1.0;
  static const double claySpreadMultiplier = 1.0;

  /// Generate light theme with custom primary color and font family
  static ThemeData lightTheme({
    Color? primaryColor,
    String? fontFamily,
    bool useMaterial3 = true,
  }) {
    final primary = primaryColor ?? defaultPrimary;
    final colorScheme = ColorScheme.fromSeed(
      seedColor: primary,
      brightness: Brightness.light,
    );

    return _buildThemeData(
      colorScheme: colorScheme,
      fontFamily: fontFamily,
      useMaterial3: useMaterial3,
    );
  }

  /// Generate dark theme with custom primary color and font family
  static ThemeData darkTheme({
    Color? primaryColor,
    String? fontFamily,
    bool useMaterial3 = true,
  }) {
    final primary = primaryColor ?? defaultPrimary;
    final colorScheme = ColorScheme.fromSeed(
      seedColor: primary,
      brightness: Brightness.dark,
    );

    return _buildThemeData(
      colorScheme: colorScheme,
      fontFamily: fontFamily,
      useMaterial3: useMaterial3,
    );
  }

  /// Private method to build theme data with common configuration
  static ThemeData _buildThemeData({
    required ColorScheme colorScheme,
    String? fontFamily,
    bool useMaterial3 = true,
  }) {
    // Create text theme with custom font family if provided
    TextTheme effectiveTextTheme = textTheme;
    if (fontFamily != null && fontFamily != 'System') {
      effectiveTextTheme = textTheme.apply(fontFamily: fontFamily);
    }

    return ThemeData(
      useMaterial3: useMaterial3,
      colorScheme: colorScheme,
      textTheme: effectiveTextTheme,
      fontFamily: fontFamily != 'System' ? fontFamily : null,

      // AppBar theme
      appBarTheme: AppBarTheme(
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        elevation: elevationS,
        centerTitle: true,
        titleTextStyle: effectiveTextTheme.headlineSmall?.copyWith(
          color: colorScheme.onSurface,
          fontWeight: FontWeight.w600,
        ),
        surfaceTintColor: colorScheme.surfaceTint,
      ),

      // Card theme
      cardTheme: CardThemeData(
        elevation: elevationS,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radiusM),
        ),
        surfaceTintColor: colorScheme.surfaceTint,
      ),

      // Button themes
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: elevationS,
          padding: const EdgeInsets.symmetric(
            horizontal: spacingL,
            vertical: spacingM,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(radiusM),
          ),
          textStyle: effectiveTextTheme.labelLarge,
        ),
      ),

      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          padding: const EdgeInsets.symmetric(
            horizontal: spacingL,
            vertical: spacingM,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(radiusM),
          ),
          textStyle: effectiveTextTheme.labelLarge,
        ),
      ),

      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          padding: const EdgeInsets.symmetric(
            horizontal: spacingL,
            vertical: spacingM,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(radiusM),
          ),
          textStyle: effectiveTextTheme.labelLarge,
        ),
      ),

      filledButtonTheme: FilledButtonThemeData(
        style: FilledButton.styleFrom(
          padding: const EdgeInsets.symmetric(
            horizontal: spacingL,
            vertical: spacingM,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(radiusM),
          ),
          textStyle: effectiveTextTheme.labelLarge,
        ),
      ),

      // Input decoration theme
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusM),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: spacingM,
          vertical: spacingM,
        ),
        labelStyle: effectiveTextTheme.bodyLarge,
        hintStyle: effectiveTextTheme.bodyLarge?.copyWith(
          color: colorScheme.onSurface.withOpacity(0.6),
        ),
      ),

      // Chip theme
      chipTheme: ChipThemeData(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radiusL),
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: spacingM,
          vertical: spacingS,
        ),
        labelStyle: effectiveTextTheme.bodySmall,
      ),

      // Divider theme
      dividerTheme: const DividerThemeData(
        thickness: 1,
        space: spacingM,
      ),

      // Navigation themes
      navigationBarTheme: NavigationBarThemeData(
        labelTextStyle: WidgetStateProperty.all(effectiveTextTheme.bodySmall),
      ),

      navigationRailTheme: NavigationRailThemeData(
        labelType: NavigationRailLabelType.all,
        unselectedLabelTextStyle: effectiveTextTheme.bodySmall,
        selectedLabelTextStyle: effectiveTextTheme.bodySmall?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),

      // List tile theme
      listTileTheme: ListTileThemeData(
        titleTextStyle: effectiveTextTheme.bodyLarge,
        subtitleTextStyle: effectiveTextTheme.bodyMedium,
        leadingAndTrailingTextStyle: effectiveTextTheme.labelLarge,
      ),

      // Dialog theme
      dialogTheme: DialogThemeData(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radiusL),
        ),
        titleTextStyle: effectiveTextTheme.headlineSmall,
        contentTextStyle: effectiveTextTheme.bodyMedium,
      ),

      // Snackbar theme
      snackBarTheme: SnackBarThemeData(
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radiusM),
        ),
        contentTextStyle: effectiveTextTheme.bodyMedium?.copyWith(
          color: colorScheme.onInverseSurface,
        ),
      ),

      // Bottom sheet theme
      bottomSheetTheme: BottomSheetThemeData(
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(radiusL),
          ),
        ),
        surfaceTintColor: colorScheme.surfaceTint,
      ),

      // Tab bar theme
      tabBarTheme: TabBarThemeData(
        labelStyle: effectiveTextTheme.titleMedium,
        unselectedLabelStyle: effectiveTextTheme.titleMedium,
        indicatorSize: TabBarIndicatorSize.label,
      ),
    );
  }

  /// Get responsive spacing based on screen size
  static double getResponsiveSpacing(double screenWidth, {
    double mobile = spacingM,
    double tablet = spacingL,
    double desktop = spacingXL,
  }) {
    if (screenWidth < mobileBreakpoint) return mobile;
    if (screenWidth < tabletBreakpoint) return tablet;
    return desktop;
  }

  /// Get responsive text scale based on screen size
  static double getResponsiveTextScale(double screenWidth) {
    if (screenWidth < mobileBreakpoint) return 0.9;
    if (screenWidth < tabletBreakpoint) return 1.0;
    return 1.1;
  }

  /// Check if color combination meets accessibility contrast requirements
  static bool meetsContrastRequirements(Color foreground, Color background, {
    double minimumRatio = 4.5, // WCAG AA standard
  }) {
    final foregroundLuminance = foreground.computeLuminance();
    final backgroundLuminance = background.computeLuminance();

    final lighter = foregroundLuminance > backgroundLuminance
        ? foregroundLuminance
        : backgroundLuminance;
    final darker = foregroundLuminance > backgroundLuminance
        ? backgroundLuminance
        : foregroundLuminance;

    final contrastRatio = (lighter + 0.05) / (darker + 0.05);
    return contrastRatio >= minimumRatio;
  }

  /// Get accessible text color for a background
  static Color getAccessibleTextColor(Color backgroundColor) {
    final luminance = backgroundColor.computeLuminance();

    // Use white text on dark backgrounds, black on light backgrounds
    if (luminance > 0.5) {
      return Colors.black87;
    } else {
      return Colors.white;
    }
  }

  /// Create high contrast color scheme for accessibility
  static ColorScheme createHighContrastColorScheme({
    required Color seedColor,
    required Brightness brightness,
  }) {
    final baseScheme = ColorScheme.fromSeed(
      seedColor: seedColor,
      brightness: brightness,
    );

    if (brightness == Brightness.dark) {
      return baseScheme.copyWith(
        surface: Colors.black,
        onSurface: Colors.white,
        primary: seedColor,
        onPrimary: getAccessibleTextColor(seedColor),
        secondary: baseScheme.secondary,
        onSecondary: getAccessibleTextColor(baseScheme.secondary),
      );
    } else {
      return baseScheme.copyWith(
        surface: Colors.white,
        onSurface: Colors.black,
        primary: seedColor,
        onPrimary: getAccessibleTextColor(seedColor),
        secondary: baseScheme.secondary,
        onSecondary: getAccessibleTextColor(baseScheme.secondary),
      );
    }
  }

  /// Get device type based on screen width
  static DeviceType getDeviceType(double screenWidth) {
    if (screenWidth < mobileBreakpoint) return DeviceType.mobile;
    if (screenWidth < tabletBreakpoint) return DeviceType.tablet;
    return DeviceType.desktop;
  }
}

/// Device type enumeration for responsive design
enum DeviceType {
  mobile,
  tablet,
  desktop,
}
