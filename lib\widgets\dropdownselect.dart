import 'package:flutter/material.dart';

/// A highly customizable dropdown select widget with full theming support
class DropdownSelect<T> extends StatelessWidget {
  /// The currently selected value
  final T? value;
  
  /// List of items to display in the dropdown
  final List<DropdownSelectItem<T>> items;
  
  /// Callback when selection changes
  final ValueChanged<T?>? onChanged;
  
  /// Label text for the dropdown
  final String? labelText;
  
  /// Hint text when no value is selected
  final String? hintText;
  
  /// Helper text below the dropdown
  final String? helperText;
  
  /// Error text to display
  final String? errorText;
  
  /// Prefix icon
  final Widget? prefixIcon;
  
  /// Suffix icon (overrides default dropdown arrow)
  final Widget? suffixIcon;
  
  /// Whether the dropdown is enabled
  final bool enabled;
  
  /// Whether the dropdown is required
  final bool required;
  
  /// Custom decoration for the input field
  final InputDecoration? decoration;
  
  /// Custom style for the dropdown button
  final ButtonStyle? buttonStyle;
  
  /// Custom text style for selected value
  final TextStyle? textStyle;
  
  /// Custom text style for hint text
  final TextStyle? hintStyle;
  
  /// Custom text style for items
  final TextStyle? itemTextStyle;
  
  /// Maximum height for the dropdown menu
  final double? menuMaxHeight;
  
  /// Width of the dropdown menu
  final double? menuWidth;
  
  /// Elevation of the dropdown menu
  final double elevation;
  
  /// Border radius for the dropdown menu
  final BorderRadius? borderRadius;
  
  /// Background color for the dropdown menu
  final Color? menuBackgroundColor;
  
  /// Padding inside the dropdown button
  final EdgeInsetsGeometry? contentPadding;
  
  /// Whether to show the dropdown arrow
  final bool showDropdownIcon;
  
  /// Custom dropdown arrow icon
  final Widget? dropdownIcon;
  
  /// Alignment for the dropdown menu
  final AlignmentGeometry menuAlignment;
  
  /// Whether to use dense layout
  final bool isDense;
  
  /// Focus node for the dropdown
  final FocusNode? focusNode;
  
  /// Whether to auto-focus the dropdown
  final bool autofocus;
  
  /// Custom validator function
  final String? Function(T?)? validator;
  
  /// Whether to auto-validate
  final AutovalidateMode? autovalidateMode;
  
  /// Animation duration for dropdown opening/closing
  final Duration animationDuration;
  
  /// Custom search functionality
  final bool enableSearch;
  
  /// Search hint text
  final String? searchHintText;
  
  /// Custom search filter function
  final bool Function(DropdownSelectItem<T>, String)? searchFilter;

  const DropdownSelect({
    super.key,
    this.value,
    required this.items,
    this.onChanged,
    this.labelText,
    this.hintText,
    this.helperText,
    this.errorText,
    this.prefixIcon,
    this.suffixIcon,
    this.enabled = true,
    this.required = false,
    this.decoration,
    this.buttonStyle,
    this.textStyle,
    this.hintStyle,
    this.itemTextStyle,
    this.menuMaxHeight,
    this.menuWidth,
    this.elevation = 8.0,
    this.borderRadius,
    this.menuBackgroundColor,
    this.contentPadding,
    this.showDropdownIcon = true,
    this.dropdownIcon,
    this.menuAlignment = AlignmentDirectional.centerStart,
    this.isDense = false,
    this.focusNode,
    this.autofocus = false,
    this.validator,
    this.autovalidateMode,
    this.animationDuration = const Duration(milliseconds: 200),
    this.enableSearch = false,
    this.searchHintText,
    this.searchFilter,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    // Build effective decoration
    final effectiveDecoration = decoration ?? InputDecoration(
      labelText: labelText,
      hintText: hintText,
      helperText: helperText,
      errorText: errorText,
      prefixIcon: prefixIcon,
      suffixIcon: showDropdownIcon ? (suffixIcon ?? dropdownIcon ?? const Icon(Icons.arrow_drop_down)) : suffixIcon,
      enabled: enabled,
      contentPadding: contentPadding ?? const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
      isDense: isDense,
      border: const OutlineInputBorder(),
    );
    
    // Build effective text styles
    final effectiveTextStyle = textStyle ?? theme.textTheme.bodyLarge;
    final effectiveHintStyle = hintStyle ?? theme.textTheme.bodyLarge?.copyWith(
      color: colorScheme.onSurface.withOpacity(0.6),
    );
    final effectiveItemTextStyle = itemTextStyle ?? theme.textTheme.bodyMedium;
    
    return FormField<T>(
      initialValue: value,
      validator: validator,
      autovalidateMode: autovalidateMode ?? AutovalidateMode.disabled,
      enabled: enabled,
      builder: (FormFieldState<T> field) {
        final hasError = field.hasError;
        final effectiveErrorText = hasError ? field.errorText : errorText;
        
        return InputDecorator(
          decoration: effectiveDecoration.copyWith(
            errorText: effectiveErrorText,
          ),
          isEmpty: value == null && (hintText == null || hintText!.isEmpty),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<T>(
              value: value,
              items: items.map((item) => DropdownMenuItem<T>(
                value: item.value,
                enabled: item.enabled,
                child: _buildItemWidget(item, effectiveItemTextStyle, context),
              )).toList(),
              onChanged: enabled ? (T? newValue) {
                field.didChange(newValue);
                onChanged?.call(newValue);
              } : null,
              hint: hintText != null ? Text(
                hintText!,
                style: effectiveHintStyle,
              ) : null,
              style: effectiveTextStyle,
              icon: const SizedBox.shrink(), // Hide default icon since we handle it in decoration
              isExpanded: true,
              menuMaxHeight: menuMaxHeight,
              elevation: elevation.toInt(),
              borderRadius: borderRadius,
              dropdownColor: menuBackgroundColor ?? colorScheme.surface,
              focusNode: focusNode,
              autofocus: autofocus,
              alignment: menuAlignment,
            ),
          ),
        );
      },
    );
  }
  
  Widget _buildItemWidget(DropdownSelectItem<T> item, TextStyle? textStyle, BuildContext context) {
    final theme = Theme.of(context);
    
    return Row(
      children: [
        if (item.leading != null) ...[
          item.leading!,
          const SizedBox(width: 12),
        ],
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                item.title,
                style: textStyle?.copyWith(
                  color: item.enabled ? null : theme.disabledColor,
                ),
              ),
              if (item.subtitle != null)
                Text(
                  item.subtitle!,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: item.enabled 
                        ? theme.colorScheme.onSurface.withOpacity(0.7)
                        : theme.disabledColor,
                  ),
                ),
            ],
          ),
        ),
        if (item.trailing != null) ...[
          const SizedBox(width: 12),
          item.trailing!,
        ],
      ],
    );
  }
}

/// Data class for dropdown select items
class DropdownSelectItem<T> {
  /// The value of this item
  final T value;
  
  /// The title text to display
  final String title;
  
  /// Optional subtitle text
  final String? subtitle;
  
  /// Optional leading widget (icon, avatar, etc.)
  final Widget? leading;
  
  /// Optional trailing widget
  final Widget? trailing;
  
  /// Whether this item is enabled
  final bool enabled;
  
  /// Optional custom text style for this item
  final TextStyle? textStyle;
  
  /// Optional tooltip for this item
  final String? tooltip;

  const DropdownSelectItem({
    required this.value,
    required this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    this.enabled = true,
    this.textStyle,
    this.tooltip,
  });
}

/// Predefined styles for DropdownSelect
class DropdownSelectStyles {
  DropdownSelectStyles._();

  /// Material 3 outlined style
  static InputDecoration outlined({
    String? labelText,
    String? hintText,
    String? helperText,
    Widget? prefixIcon,
    Widget? suffixIcon,
    bool enabled = true,
  }) {
    return InputDecoration(
      labelText: labelText,
      hintText: hintText,
      helperText: helperText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      enabled: enabled,
      border: const OutlineInputBorder(),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
    );
  }

  /// Material 3 filled style
  static InputDecoration filled({
    String? labelText,
    String? hintText,
    String? helperText,
    Widget? prefixIcon,
    Widget? suffixIcon,
    bool enabled = true,
    Color? fillColor,
  }) {
    return InputDecoration(
      labelText: labelText,
      hintText: hintText,
      helperText: helperText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      enabled: enabled,
      filled: true,
      fillColor: fillColor,
      border: const OutlineInputBorder(
        borderSide: BorderSide.none,
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
    );
  }

  /// Underlined style
  static InputDecoration underlined({
    String? labelText,
    String? hintText,
    String? helperText,
    Widget? prefixIcon,
    Widget? suffixIcon,
    bool enabled = true,
  }) {
    return InputDecoration(
      labelText: labelText,
      hintText: hintText,
      helperText: helperText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      enabled: enabled,
      border: const UnderlineInputBorder(),
      contentPadding: const EdgeInsets.symmetric(horizontal: 0, vertical: 16),
    );
  }

  /// Compact style for dense layouts
  static InputDecoration compact({
    String? labelText,
    String? hintText,
    Widget? prefixIcon,
    Widget? suffixIcon,
    bool enabled = true,
  }) {
    return InputDecoration(
      labelText: labelText,
      hintText: hintText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      enabled: enabled,
      isDense: true,
      border: const OutlineInputBorder(),
      contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
    );
  }
}

/// Factory constructors for common dropdown types
extension DropdownSelectFactory<T> on DropdownSelect<T> {
  /// Create a theme mode dropdown
  static DropdownSelect<ThemeMode> themeMode({
    ThemeMode? value,
    ValueChanged<ThemeMode?>? onChanged,
    String? labelText = 'Theme Mode',
    bool enabled = true,
    InputDecoration? decoration,
  }) {
    return DropdownSelect<ThemeMode>(
      value: value,
      onChanged: onChanged,
      enabled: enabled,
      decoration: decoration ?? DropdownSelectStyles.outlined(
        labelText: labelText,
        prefixIcon: const Icon(Icons.brightness_6),
      ),
      items: const [
        DropdownSelectItem(
          value: ThemeMode.system,
          title: 'System',
          subtitle: 'Follow system settings',
          leading: Icon(Icons.settings_suggest),
        ),
        DropdownSelectItem(
          value: ThemeMode.light,
          title: 'Light',
          subtitle: 'Always use light theme',
          leading: Icon(Icons.light_mode),
        ),
        DropdownSelectItem(
          value: ThemeMode.dark,
          title: 'Dark',
          subtitle: 'Always use dark theme',
          leading: Icon(Icons.dark_mode),
        ),
      ],
    );
  }

  /// Create a font family dropdown
  static DropdownSelect<String> fontFamily({
    String? value,
    ValueChanged<String?>? onChanged,
    String? labelText = 'Font Family',
    bool enabled = true,
    InputDecoration? decoration,
    List<String>? fontFamilies,
  }) {
    final fonts = fontFamilies ?? [
      'System',
      'Roboto',
      'Open Sans',
      'Lato',
      'Montserrat',
      'Poppins',
      'Inter',
      'Source Sans Pro',
    ];

    return DropdownSelect<String>(
      value: value,
      onChanged: onChanged,
      enabled: enabled,
      decoration: decoration ?? DropdownSelectStyles.outlined(
        labelText: labelText,
        prefixIcon: const Icon(Icons.font_download),
      ),
      items: fonts.map((font) => DropdownSelectItem(
        value: font,
        title: font == 'System' ? 'System Default' : font,
        leading: const Icon(Icons.text_fields),
      )).toList(),
    );
  }

  /// Create a language dropdown
  static DropdownSelect<String> language({
    String? value,
    ValueChanged<String?>? onChanged,
    String? labelText = 'Language',
    bool enabled = true,
    InputDecoration? decoration,
    Map<String, String>? languages,
  }) {
    final langs = languages ?? {
      'en': 'English',
      'es': 'Español',
      'fr': 'Français',
      'de': 'Deutsch',
      'it': 'Italiano',
      'pt': 'Português',
      'ru': 'Русский',
      'zh': '中文',
      'ja': '日本語',
      'ko': '한국어',
    };

    return DropdownSelect<String>(
      value: value,
      onChanged: onChanged,
      enabled: enabled,
      decoration: decoration ?? DropdownSelectStyles.outlined(
        labelText: labelText,
        prefixIcon: const Icon(Icons.language),
      ),
      items: langs.entries.map((entry) => DropdownSelectItem(
        value: entry.key,
        title: entry.value,
        leading: const Icon(Icons.translate),
      )).toList(),
    );
  }

  /// Create a currency dropdown
  static DropdownSelect<String> currency({
    String? value,
    ValueChanged<String?>? onChanged,
    String? labelText = 'Currency',
    bool enabled = true,
    InputDecoration? decoration,
    Map<String, String>? currencies,
  }) {
    final currs = currencies ?? {
      'USD': 'US Dollar (\$)',
      'EUR': 'Euro (€)',
      'GBP': 'British Pound (£)',
      'JPY': 'Japanese Yen (¥)',
      'CAD': 'Canadian Dollar (C\$)',
      'AUD': 'Australian Dollar (A\$)',
      'CHF': 'Swiss Franc (CHF)',
      'CNY': 'Chinese Yuan (¥)',
      'INR': 'Indian Rupee (₹)',
      'BRL': 'Brazilian Real (R\$)',
    };

    return DropdownSelect<String>(
      value: value,
      onChanged: onChanged,
      enabled: enabled,
      decoration: decoration ?? DropdownSelectStyles.outlined(
        labelText: labelText,
        prefixIcon: const Icon(Icons.attach_money),
      ),
      items: currs.entries.map((entry) => DropdownSelectItem(
        value: entry.key,
        title: entry.value,
        leading: const Icon(Icons.monetization_on),
      )).toList(),
    );
  }
}
