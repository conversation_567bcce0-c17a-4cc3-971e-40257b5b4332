import 'package:flutter/material.dart';
import 'package:flex_color_picker/flex_color_picker.dart';
import 'thememanager.dart';
import 'themeconfig.dart';

/// Advanced theme customization widget with color picker
class ThemeCustomizer extends StatefulWidget {
  const ThemeCustomizer({super.key});

  @override
  State<ThemeCustomizer> createState() => _ThemeCustomizerState();
}

class _ThemeCustomizerState extends State<ThemeCustomizer> with TickerProviderStateMixin {
  late TabController _tabController;
  late ThemeManager _themeManager;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _themeManager = ThemeManager.instance;
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Theme Customizer'),
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(icon: Icon(Icons.palette), text: 'Colors'),
            Tab(icon: Icon(Icons.brightness_6), text: 'Mode'),
            Tab(icon: Icon(Icons.style), text: 'Style'),
            Tab(icon: Icon(Icons.accessibility), text: 'Accessibility'),
            Tab(icon: Icon(Icons.preview), text: 'Preview'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => _showResetDialog(),
            tooltip: 'Reset to Default',
          ),
        ],
      ),
      body: ListenableBuilder(
        listenable: _themeManager,
        builder: (context, child) {
          return TabBarView(
            controller: _tabController,
            children: [
              _buildColorsTab(),
              _buildModeTab(),
              _buildStyleTab(),
              _buildAccessibilityTab(),
              _buildPreviewTab(),
            ],
          );
        },
      ),
    );
  }

  Widget _buildColorsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(ThemeConfig.spacingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Predefined Color Schemes
          Card(
            child: Padding(
              padding: const EdgeInsets.all(ThemeConfig.spacingM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Predefined Schemes',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: ThemeConfig.spacingM),
                  Wrap(
                    spacing: ThemeConfig.spacingS,
                    runSpacing: ThemeConfig.spacingS,
                    children: ThemeConfig.colorSchemes.entries.map((entry) {
                      final isSelected = _themeManager.primaryColor == entry.value;
                      return FilterChip(
                        label: Text(entry.key),
                        selected: isSelected,
                        onSelected: (selected) {
                          if (selected) {
                            _themeManager.setPrimaryColor(entry.value);
                          }
                        },
                        avatar: CircleAvatar(
                          backgroundColor: entry.value,
                          radius: 8,
                        ),
                      );
                    }).toList(),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: ThemeConfig.spacingM),

          // Custom Primary Color
          Card(
            child: Padding(
              padding: const EdgeInsets.all(ThemeConfig.spacingM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Custom Primary Color',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: ThemeConfig.spacingM),
                  ColorPicker(
                    color: _themeManager.primaryColor,
                    onColorChanged: (color) {
                      _themeManager.setPrimaryColor(color);
                    },
                    width: 40,
                    height: 40,
                    borderRadius: ThemeConfig.radiusM,
                    spacing: ThemeConfig.spacingS,
                    runSpacing: ThemeConfig.spacingS,
                    wheelDiameter: 200,
                    heading: Text(
                      'Select Primary Color',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    subheading: Text(
                      'Choose your brand color',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    wheelSubheading: Text(
                      'Color Wheel',
                      style: Theme.of(context).textTheme.titleSmall,
                    ),
                    showMaterialName: true,
                    showColorName: true,
                    showColorCode: true,
                    copyPasteBehavior: const ColorPickerCopyPasteBehavior(
                      longPressMenu: true,
                    ),
                    materialNameTextStyle: Theme.of(context).textTheme.bodySmall,
                    colorNameTextStyle: Theme.of(context).textTheme.bodySmall,
                    colorCodeTextStyle: Theme.of(context).textTheme.bodySmall,
                    pickersEnabled: const <ColorPickerType, bool>{
                      ColorPickerType.both: false,
                      ColorPickerType.primary: true,
                      ColorPickerType.accent: false,
                      ColorPickerType.bw: false,
                      ColorPickerType.custom: true,
                      ColorPickerType.wheel: true,
                    },
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: ThemeConfig.spacingM),

          // Color Harmony
          Card(
            child: Padding(
              padding: const EdgeInsets.all(ThemeConfig.spacingM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Color Harmony',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: ThemeConfig.spacingM),
                  
                  // Complementary Color
                  ListTile(
                    leading: CircleAvatar(
                      backgroundColor: _themeManager.getComplementaryColor(),
                    ),
                    title: const Text('Complementary'),
                    subtitle: Text(
                      '#${_themeManager.getComplementaryColor().value.toRadixString(16).padLeft(8, '0').substring(2)}',
                    ),
                    trailing: IconButton(
                      icon: const Icon(Icons.color_lens),
                      onPressed: () {
                        _themeManager.setPrimaryColor(_themeManager.getComplementaryColor());
                      },
                    ),
                  ),
                  
                  // Analogous Colors
                  const Text('Analogous Colors'),
                  const SizedBox(height: ThemeConfig.spacingS),
                  Row(
                    children: _themeManager.getAnalogousColors().map((color) {
                      return Expanded(
                        child: GestureDetector(
                          onTap: () => _themeManager.setPrimaryColor(color),
                          child: Container(
                            height: 40,
                            margin: const EdgeInsets.symmetric(horizontal: 2),
                            decoration: BoxDecoration(
                              color: color,
                              borderRadius: BorderRadius.circular(ThemeConfig.radiusS),
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                  
                  const SizedBox(height: ThemeConfig.spacingM),
                  
                  // Triadic Colors
                  const Text('Triadic Colors'),
                  const SizedBox(height: ThemeConfig.spacingS),
                  Row(
                    children: _themeManager.getTriadicColors().map((color) {
                      return Expanded(
                        child: GestureDetector(
                          onTap: () => _themeManager.setPrimaryColor(color),
                          child: Container(
                            height: 40,
                            margin: const EdgeInsets.symmetric(horizontal: 2),
                            decoration: BoxDecoration(
                              color: color,
                              borderRadius: BorderRadius.circular(ThemeConfig.radiusS),
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModeTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(ThemeConfig.spacingM),
      child: Column(
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(ThemeConfig.spacingM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Theme Mode',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: ThemeConfig.spacingM),
                  
                  RadioListTile<ThemeMode>(
                    title: const Text('System'),
                    subtitle: const Text('Follow system settings'),
                    value: ThemeMode.system,
                    groupValue: _themeManager.themeMode,
                    onChanged: (mode) {
                      if (mode != null) {
                        _themeManager.setThemeMode(mode);
                      }
                    },
                  ),
                  
                  RadioListTile<ThemeMode>(
                    title: const Text('Light'),
                    subtitle: const Text('Always use light theme'),
                    value: ThemeMode.light,
                    groupValue: _themeManager.themeMode,
                    onChanged: (mode) {
                      if (mode != null) {
                        _themeManager.setThemeMode(mode);
                      }
                    },
                  ),
                  
                  RadioListTile<ThemeMode>(
                    title: const Text('Dark'),
                    subtitle: const Text('Always use dark theme'),
                    value: ThemeMode.dark,
                    groupValue: _themeManager.themeMode,
                    onChanged: (mode) {
                      if (mode != null) {
                        _themeManager.setThemeMode(mode);
                      }
                    },
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: ThemeConfig.spacingM),

          Card(
            child: Padding(
              padding: const EdgeInsets.all(ThemeConfig.spacingM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Current Status',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: ThemeConfig.spacingM),
                  
                  ListTile(
                    leading: Icon(
                      _themeManager.isDarkMode(context) 
                          ? Icons.dark_mode 
                          : Icons.light_mode,
                    ),
                    title: Text(
                      'Current: ${_themeManager.isDarkMode(context) ? 'Dark' : 'Light'}',
                    ),
                    subtitle: Text(
                      'Mode: ${_themeManager.getThemeModeDisplayName()}',
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStyleTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(ThemeConfig.spacingM),
      child: Column(
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(ThemeConfig.spacingM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Design System',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: ThemeConfig.spacingM),
                  
                  SwitchListTile(
                    title: const Text('Material 3'),
                    subtitle: const Text('Use Material You design system'),
                    value: _themeManager.useMaterial3,
                    onChanged: (value) {
                      _themeManager.setUseMaterial3(value);
                    },
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: ThemeConfig.spacingM),

          Card(
            child: Padding(
              padding: const EdgeInsets.all(ThemeConfig.spacingM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Typography',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: ThemeConfig.spacingM),
                  
                  DropdownButtonFormField<String>(
                    value: _themeManager.fontFamily,
                    decoration: const InputDecoration(
                      labelText: 'Font Family',
                      border: OutlineInputBorder(),
                    ),
                    items: ThemeConfig.availableFontFamilies.map((fontFamily) {
                      return DropdownMenuItem(
                        value: fontFamily,
                        child: Text(fontFamily == 'System' ? 'System Default' : fontFamily),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        _themeManager.setFontFamily(value);
                      }
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccessibilityTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(ThemeConfig.spacingM),
      child: Column(
        children: [
          // High Contrast Mode
          Card(
            child: Padding(
              padding: const EdgeInsets.all(ThemeConfig.spacingM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Visual Accessibility',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: ThemeConfig.spacingM),

                  SwitchListTile(
                    title: const Text('High Contrast Mode'),
                    subtitle: const Text('Improves visibility for users with visual impairments'),
                    value: _themeManager.highContrastMode,
                    onChanged: (value) {
                      _themeManager.setHighContrastMode(value);
                    },
                  ),

                  const SizedBox(height: ThemeConfig.spacingM),

                  Text(
                    'Text Scale Factor: ${_themeManager.textScaleFactor.toStringAsFixed(1)}x',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: ThemeConfig.spacingS),

                  Slider(
                    value: _themeManager.textScaleFactor,
                    min: 0.8,
                    max: 2.0,
                    divisions: 12,
                    label: '${_themeManager.textScaleFactor.toStringAsFixed(1)}x',
                    onChanged: (value) {
                      _themeManager.setTextScaleFactor(value);
                    },
                  ),

                  Text(
                    'Adjust text size for better readability',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: ThemeConfig.spacingM),

          // Accessibility Status
          Card(
            child: Padding(
              padding: const EdgeInsets.all(ThemeConfig.spacingM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Accessibility Status',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: ThemeConfig.spacingM),

                  ListTile(
                    leading: Icon(
                      _themeManager.meetsAccessibilityStandards(context)
                          ? Icons.check_circle
                          : Icons.warning,
                      color: _themeManager.meetsAccessibilityStandards(context)
                          ? Colors.green
                          : Colors.orange,
                    ),
                    title: Text(
                      _themeManager.meetsAccessibilityStandards(context)
                          ? 'Meets WCAG AA Standards'
                          : 'May not meet accessibility standards',
                    ),
                    subtitle: const Text(
                      'Color contrast ratio for text readability',
                    ),
                  ),

                  ListTile(
                    leading: Icon(
                      _themeManager.textScaleFactor >= 1.2
                          ? Icons.check_circle
                          : Icons.info,
                      color: _themeManager.textScaleFactor >= 1.2
                          ? Colors.green
                          : Colors.blue,
                    ),
                    title: Text(
                      _themeManager.textScaleFactor >= 1.2
                          ? 'Large text enabled'
                          : 'Standard text size',
                    ),
                    subtitle: const Text(
                      'Recommended for users with visual impairments',
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: ThemeConfig.spacingM),

          // Quick Accessibility Presets
          Card(
            child: Padding(
              padding: const EdgeInsets.all(ThemeConfig.spacingM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Quick Presets',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: ThemeConfig.spacingM),

                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () {
                            _themeManager.setHighContrastMode(true);
                            _themeManager.setTextScaleFactor(1.3);
                          },
                          icon: const Icon(Icons.accessibility),
                          label: const Text('High Accessibility'),
                        ),
                      ),
                      const SizedBox(width: ThemeConfig.spacingS),
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () {
                            _themeManager.setHighContrastMode(false);
                            _themeManager.setTextScaleFactor(1.0);
                          },
                          icon: const Icon(Icons.restore),
                          label: const Text('Standard'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPreviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(ThemeConfig.spacingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Theme Preview',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const SizedBox(height: ThemeConfig.spacingM),
          
          // Color Palette Preview
          Card(
            child: Padding(
              padding: const EdgeInsets.all(ThemeConfig.spacingM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Color Palette',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: ThemeConfig.spacingM),
                  
                  Wrap(
                    spacing: ThemeConfig.spacingS,
                    runSpacing: ThemeConfig.spacingS,
                    children: [
                      _buildColorSwatch('Primary', Theme.of(context).colorScheme.primary),
                      _buildColorSwatch('Secondary', Theme.of(context).colorScheme.secondary),
                      _buildColorSwatch('Tertiary', Theme.of(context).colorScheme.tertiary),
                      _buildColorSwatch('Surface', Theme.of(context).colorScheme.surface),
                      _buildColorSwatch('Error', Theme.of(context).colorScheme.error),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: ThemeConfig.spacingM),

          // Component Preview
          Card(
            child: Padding(
              padding: const EdgeInsets.all(ThemeConfig.spacingM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Components',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: ThemeConfig.spacingM),
                  
                  // Buttons
                  Wrap(
                    spacing: ThemeConfig.spacingS,
                    runSpacing: ThemeConfig.spacingS,
                    children: [
                      ElevatedButton(
                        onPressed: () {},
                        child: const Text('Elevated'),
                      ),
                      OutlinedButton(
                        onPressed: () {},
                        child: const Text('Outlined'),
                      ),
                      TextButton(
                        onPressed: () {},
                        child: const Text('Text'),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: ThemeConfig.spacingM),
                  
                  // Form Fields
                  const TextField(
                    decoration: InputDecoration(
                      labelText: 'Text Field',
                      hintText: 'Enter text here',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  
                  const SizedBox(height: ThemeConfig.spacingM),
                  
                  // Chips
                  Wrap(
                    spacing: ThemeConfig.spacingS,
                    children: [
                      Chip(
                        label: const Text('Chip'),
                        onDeleted: () {},
                      ),
                      FilterChip(
                        label: const Text('Filter'),
                        selected: true,
                        onSelected: (bool value) {},
                      ),
                      ActionChip(
                        label: const Text('Action'),
                        onPressed: () {},
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildColorSwatch(String name, Color color) {
    return Column(
      children: [
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(ThemeConfig.radiusM),
            border: Border.all(color: Colors.grey.withOpacity(0.3)),
          ),
        ),
        const SizedBox(height: ThemeConfig.spacingS),
        Text(
          name,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  void _showResetDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Theme'),
        content: const Text('Are you sure you want to reset all theme settings to default?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              _themeManager.resetToDefault();
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Theme reset to default')),
              );
            },
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }
}
