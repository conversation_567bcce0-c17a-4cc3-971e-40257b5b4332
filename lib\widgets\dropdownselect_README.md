# DropdownSelect Widget

A highly customizable dropdown select widget for Flutter with full theming support and preset configurations.

## Features

### 🎨 **Full Customization**
- Complete theming support with Material 3 design
- Custom text styles, colors, and decorations
- Flexible item layouts with leading/trailing widgets
- Multiple decoration styles (outlined, filled, underlined, compact)

### 🚀 **Built-in Presets**
- Theme mode selector (Light/Dark/System)
- Font family selector
- Language selector
- Currency selector
- Easy factory constructors for common use cases

### ♿ **Accessibility**
- Full keyboard navigation support
- Screen reader compatibility
- Focus management
- Validation support with error states

### 🔧 **Developer Experience**
- Type-safe generic implementation
- Rich item data model with subtitles and icons
- Form field integration with validation
- Consistent API with Flutter's form widgets

## Basic Usage

### Simple Dropdown

```dart
DropdownSelect<String>(
  value: selectedValue,
  onChanged: (value) => setState(() => selectedValue = value),
  decoration: DropdownSelectStyles.outlined(
    labelText: 'Select Option',
    prefixIcon: Icon(Icons.list),
  ),
  items: const [
    DropdownSelectItem(value: 'option1', title: 'Option 1'),
    DropdownSelectItem(value: 'option2', title: 'Option 2'),
    DropdownSelectItem(value: 'option3', title: 'Option 3'),
  ],
)
```

### Rich Items with Icons and Subtitles

```dart
DropdownSelect<String>(
  value: selectedPriority,
  onChanged: (value) => setState(() => selectedPriority = value),
  items: const [
    DropdownSelectItem(
      value: 'high',
      title: 'High Priority',
      subtitle: 'Urgent tasks requiring immediate attention',
      leading: Icon(Icons.priority_high, color: Colors.red),
      trailing: Chip(label: Text('Urgent')),
    ),
    DropdownSelectItem(
      value: 'medium',
      title: 'Medium Priority',
      subtitle: 'Important but not urgent',
      leading: Icon(Icons.remove, color: Colors.orange),
    ),
    DropdownSelectItem(
      value: 'low',
      title: 'Low Priority',
      subtitle: 'Can be done when time permits',
      leading: Icon(Icons.keyboard_arrow_down, color: Colors.green),
    ),
  ],
)
```

## Preset Widgets

### Theme Mode Selector

```dart
DropdownSelectFactory.themeMode(
  value: currentThemeMode,
  onChanged: (mode) => updateThemeMode(mode),
  labelText: 'Theme Mode',
)
```

### Font Family Selector

```dart
DropdownSelectFactory.fontFamily(
  value: currentFont,
  onChanged: (font) => updateFont(font),
  fontFamilies: ['System', 'Roboto', 'Open Sans', 'Lato'],
)
```

### Language Selector

```dart
DropdownSelectFactory.language(
  value: currentLanguage,
  onChanged: (lang) => updateLanguage(lang),
  languages: {
    'en': 'English',
    'es': 'Español',
    'fr': 'Français',
  },
)
```

### Currency Selector

```dart
DropdownSelectFactory.currency(
  value: currentCurrency,
  onChanged: (currency) => updateCurrency(currency),
)
```

## Styling Options

### Outlined Style (Default)

```dart
decoration: DropdownSelectStyles.outlined(
  labelText: 'Select Option',
  hintText: 'Choose one...',
  prefixIcon: Icon(Icons.list),
)
```

### Filled Style

```dart
decoration: DropdownSelectStyles.filled(
  labelText: 'Select Option',
  fillColor: Colors.grey.shade100,
  prefixIcon: Icon(Icons.list),
)
```

### Underlined Style

```dart
decoration: DropdownSelectStyles.underlined(
  labelText: 'Select Option',
  prefixIcon: Icon(Icons.list),
)
```

### Compact Style

```dart
decoration: DropdownSelectStyles.compact(
  labelText: 'Select Option',
  prefixIcon: Icon(Icons.list),
)
```

## Advanced Features

### Form Validation

```dart
DropdownSelect<String>(
  value: selectedValue,
  onChanged: (value) => setState(() => selectedValue = value),
  validator: (value) {
    if (value == null || value.isEmpty) {
      return 'Please select an option';
    }
    return null;
  },
  autovalidateMode: AutovalidateMode.onUserInteraction,
  items: items,
)
```

### Disabled State

```dart
DropdownSelect<String>(
  value: selectedValue,
  onChanged: null, // Makes it disabled
  enabled: false,
  items: items,
)
```

### Custom Menu Properties

```dart
DropdownSelect<String>(
  value: selectedValue,
  onChanged: (value) => setState(() => selectedValue = value),
  menuMaxHeight: 300,
  elevation: 12,
  borderRadius: BorderRadius.circular(16),
  menuBackgroundColor: Colors.white,
  items: items,
)
```

## DropdownSelectItem Properties

```dart
DropdownSelectItem<T>(
  value: T,                    // The value of this item
  title: String,               // Main display text
  subtitle: String?,           // Optional subtitle text
  leading: Widget?,            // Leading widget (icon, avatar, etc.)
  trailing: Widget?,           // Trailing widget (badge, icon, etc.)
  enabled: bool,               // Whether item is selectable
  textStyle: TextStyle?,       // Custom text style for this item
  tooltip: String?,            // Tooltip text
)
```

## Customization Parameters

### Core Properties
- `value`: Currently selected value
- `items`: List of dropdown items
- `onChanged`: Selection change callback
- `enabled`: Whether dropdown is interactive

### Styling
- `decoration`: Input field decoration
- `textStyle`: Text style for selected value
- `hintStyle`: Text style for hint text
- `itemTextStyle`: Text style for dropdown items

### Layout
- `contentPadding`: Padding inside the dropdown
- `isDense`: Whether to use dense layout
- `menuMaxHeight`: Maximum height of dropdown menu
- `menuWidth`: Width of dropdown menu

### Behavior
- `autofocus`: Whether to auto-focus
- `focusNode`: Custom focus node
- `validator`: Validation function
- `autovalidateMode`: When to validate

## Integration with Theme System

The DropdownSelect widget automatically adapts to your app's theme:

```dart
// Automatically uses theme colors and text styles
DropdownSelect<String>(
  // Widget automatically inherits:
  // - Primary colors from ColorScheme
  // - Text styles from TextTheme
  // - Border styles from InputDecorationTheme
  // - Elevation and shadows from theme
)
```

## Best Practices

### 1. Use Factory Constructors for Common Cases
```dart
// ✅ Good - Use preset for common dropdowns
DropdownSelectFactory.themeMode(...)

// ❌ Avoid - Manual setup for common cases
DropdownSelect<ThemeMode>(
  items: [
    DropdownSelectItem(value: ThemeMode.light, title: 'Light'),
    // ... manual setup
  ],
)
```

### 2. Provide Meaningful Subtitles
```dart
// ✅ Good - Helpful subtitle
DropdownSelectItem(
  value: 'premium',
  title: 'Premium Plan',
  subtitle: 'All features included - \$29/month',
)

// ❌ Avoid - Redundant subtitle
DropdownSelectItem(
  value: 'premium',
  title: 'Premium',
  subtitle: 'Premium',
)
```

### 3. Use Appropriate Icons
```dart
// ✅ Good - Meaningful icons
DropdownSelectItem(
  value: 'urgent',
  title: 'Urgent',
  leading: Icon(Icons.warning, color: Colors.red),
)
```

### 4. Handle Validation Properly
```dart
// ✅ Good - Clear validation messages
validator: (value) {
  if (value == null) return 'Please select a priority level';
  return null;
}
```

## Performance Considerations

- Items are built lazily when dropdown opens
- Use `const` constructors for static items
- Avoid heavy computations in item builders
- Consider using `AutovalidateMode.onUserInteraction` for better UX

## Accessibility

The widget includes built-in accessibility features:
- Semantic labels for screen readers
- Keyboard navigation support
- Focus management
- High contrast support
- Proper ARIA attributes

## Migration from Standard DropdownButton

```dart
// Old DropdownButton
DropdownButton<String>(
  value: value,
  items: items.map((item) => DropdownMenuItem(
    value: item,
    child: Text(item),
  )).toList(),
  onChanged: onChanged,
)

// New DropdownSelect
DropdownSelect<String>(
  value: value,
  items: items.map((item) => DropdownSelectItem(
    value: item,
    title: item,
  )).toList(),
  onChanged: onChanged,
  decoration: DropdownSelectStyles.outlined(),
)
```
