
/// import 'package:microrealesate/widgets/theme/theme.dart';
/// 
/// // Wrap your app with ThemeProvider
/// ThemeProvider(
///   child: MyApp(),
/// )
/// 
/// // Access theme manager anywhere
/// context.themeManager.setPrimaryColor(Colors.blue);
/// 
/// // Use theme-aware widgets
/// ThemeAware(
///   builder: (context, theme) => Container(
///     color: theme.colorScheme.primary,
///   ),
/// )
/// ```

// Core theme system
export 'themeconfig.dart';
export 'thememanager.dart';
export 'themeprovider.dart';
export 'themeutils.dart';

// Appearance customization UI
export 'themecustomizer.dart';

// Re-export flex_color_picker for convenience
export 'package:flex_color_picker/flex_color_picker.dart';

import 'package:flutter/material.dart';
import 'themeconfig.dart';
import 'thememanager.dart';

/// Quick access to theme system
class MicroRealEstateTheme {
  MicroRealEstateTheme._();

  /// Initialize the theme system
  static Future<void> initialize() async {
    // Theme manager will automatically load saved settings when accessed
    ThemeManager.instance;
  }

  /// Get the current theme manager instance
  static ThemeManager get manager => ThemeManager.instance;

  /// Quick theme mode setters
  static Future<void> setLightMode() => manager.setThemeMode(ThemeMode.light);
  static Future<void> setDarkMode() => manager.setThemeMode(ThemeMode.dark);
  static Future<void> setSystemMode() => manager.setThemeMode(ThemeMode.system);

  /// Quick color scheme setters
  static Future<void> setBlueTheme() => manager.setPrimaryColor(ThemeConfig.primaryBlue);
  static Future<void> setGreenTheme() => manager.setPrimaryColor(ThemeConfig.primaryGreen);
  static Future<void> setOrangeTheme() => manager.setPrimaryColor(ThemeConfig.primaryOrange);
  static Future<void> setPropertyTheme() => manager.setPrimaryColor(ThemeConfig.propertyGreen);
  static Future<void> setTenantTheme() => manager.setPrimaryColor(ThemeConfig.tenantBlue);
  static Future<void> setLeaseTheme() => manager.setPrimaryColor(ThemeConfig.leaseOrange);

  /// Real estate specific themes with complete configurations
  static const Map<String, Map<String, dynamic>> realEstateThemes = {
    'Property Management': {
      'color': ThemeConfig.propertyGreen,
      'description': 'Professional green theme for property management',
      'fontFamily': 'Roboto',
      'highContrast': false,
    },
    'Tenant Portal': {
      'color': ThemeConfig.tenantBlue,
      'description': 'Friendly blue theme for tenant interactions',
      'fontFamily': 'Open Sans',
      'highContrast': false,
    },
    'Lease Management': {
      'color': ThemeConfig.leaseOrange,
      'description': 'Energetic orange theme for lease operations',
      'fontFamily': 'Lato',
      'highContrast': false,
    },
    'Document Center': {
      'color': ThemeConfig.documentPurple,
      'description': 'Professional purple theme for document management',
      'fontFamily': 'Inter',
      'highContrast': false,
    },
    'Financial Dashboard': {
      'color': ThemeConfig.financialGold,
      'description': 'Premium gold theme for financial data',
      'fontFamily': 'Montserrat',
      'highContrast': false,
    },
    'Accessibility Mode': {
      'color': ThemeConfig.primaryBlue,
      'description': 'High contrast theme optimized for accessibility',
      'fontFamily': 'System',
      'highContrast': true,
    },
  };

  /// Apply real estate specific theme with full configuration
  static Future<void> applyRealEstateTheme(String themeName) async {
    final themeConfig = realEstateThemes[themeName];
    if (themeConfig != null) {
      await manager.setPrimaryColor(themeConfig['color'] as Color);
      await manager.setFontFamily(themeConfig['fontFamily'] as String);
      await manager.setHighContrastMode(themeConfig['highContrast'] as bool);

      // Set appropriate text scale for accessibility theme
      if (themeName == 'Accessibility Mode') {
        await manager.setTextScaleFactor(1.3);
      }
    }
  }

  /// Get current theme statistics
  static Map<String, dynamic> getStatistics() => manager.getThemeStatistics();

  /// Export current theme settings
  static Map<String, dynamic> exportSettings() => manager.exportThemeSettings();

  /// Import theme settings
  static Future<void> importSettings(Map<String, dynamic> settings) =>
      manager.importThemeSettings(settings);

  /// Reset to default theme
  static Future<void> resetToDefault() => manager.resetToDefault();

  /// Enable accessibility mode with optimized settings
  static Future<void> enableAccessibilityMode() async {
    await manager.setHighContrastMode(true);
    await manager.setTextScaleFactor(1.3);
    await manager.setUseMaterial3(true); // Material 3 has better accessibility
  }

  /// Disable accessibility mode and return to standard settings
  static Future<void> disableAccessibilityMode() async {
    await manager.setHighContrastMode(false);
    await manager.setTextScaleFactor(1.0);
  }

  /// Check if current theme meets accessibility standards
  static bool meetsAccessibilityStandards(BuildContext context) =>
      manager.meetsAccessibilityStandards(context);

  /// Get device type for responsive design
  static DeviceType getDeviceType(BuildContext context) =>
      manager.getDeviceType(context);

  /// Get responsive spacing for current screen
  static double getResponsiveSpacing(BuildContext context) =>
      manager.getResponsiveSpacing(context);

  /// Validate theme configuration
  static Map<String, bool> validateThemeConfiguration() {
    final stats = manager.getThemeStatistics();
    return {
      'has_valid_colors': stats['primary_color_hex'] != null,
      'has_valid_font': stats['font_family'] != null,
      'accessibility_ready': stats['high_contrast_mode'] == true ||
                           stats['text_scale_factor'] >= 1.2,
      'material3_enabled': stats['using_material3'] == true,
    };
  }

  /// Get theme recommendations based on current settings
  static List<String> getThemeRecommendations(BuildContext context) {
    final recommendations = <String>[];
    final manager = MicroRealEstateTheme.manager;

    if (!manager.meetsAccessibilityStandards(context)) {
      recommendations.add('Consider enabling high contrast mode for better accessibility');
    }

    if (manager.textScaleFactor < 1.2) {
      recommendations.add('Increase text scale for better readability');
    }

    if (!manager.useMaterial3) {
      recommendations.add('Enable Material 3 for modern design and better accessibility');
    }

    final deviceType = manager.getDeviceType(context);
    if (deviceType == DeviceType.desktop && manager.fontFamily == 'System') {
      recommendations.add('Consider using a custom font family for better desktop experience');
    }

    return recommendations;
  }
}
