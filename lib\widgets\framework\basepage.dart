import 'package:flutter/material.dart';
import 'package:microrealesate/widgets/framework/responsiveconfig.dart';
import 'package:responsive_builder/responsive_builder.dart';
import '../../widgets/theme/theme.dart';


abstract class BasePage extends StatefulWidget {
  final String title;
  final bool showAppBar;
  final bool showBottomNavigation;
  final bool showNavigationRail;
  final bool showFloatingActionButton;
  final List<Widget>? appBarActions;
  final Widget? floatingActionButton;
  final Color? backgroundColor;
  final bool centerContent;
  final bool showBackButton;
  final EdgeInsets? customPadding;
  final double? maxContentWidth;

  const BasePage({
    super.key,
    required this.title,
    this.showAppBar = true,
    this.showBottomNavigation = true,
    this.showNavigationRail = true,
    this.showFloatingActionButton = false,
    this.appBarActions,
    this.floatingActionButton,
    this.backgroundColor,
    this.centerContent = false,
    this.showBackButton = true,
    this.customPadding,
    this.maxContentWidth,
  });

  /// Build the main content for mobile devices
  Widget buildMobileContent(BuildContext context);

  /// Build the main content for tablet devices
  Widget buildTabletContent(BuildContext context) => buildMobileContent(context);

  /// Build the main content for desktop devices
  Widget buildDesktopContent(BuildContext context) => buildTabletContent(context);

  /// Build custom app bar if needed
  PreferredSizeWidget? buildAppBar(BuildContext context) => null;

  /// Build custom bottom navigation if needed
  Widget? buildBottomNavigation(BuildContext context) => null;

  /// Build custom navigation rail if needed
  Widget? buildNavigationRail(BuildContext context) => null;

  /// Build custom floating action button if needed
  Widget? buildFloatingActionButton(BuildContext context) => floatingActionButton;

  /// Get page-specific navigation items
  List<NavigationItem> getNavigationItems() => [];

  /// Handle navigation item selection
  void onNavigationItemSelected(int index) {}
}

/// Base page state with responsive functionality
abstract class BasePageState<T extends BasePage> extends State<T> {
  int _selectedNavigationIndex = 0;

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, sizingInformation) {
        return Scaffold(
          backgroundColor: widget.backgroundColor ?? Theme.of(context).scaffoldBackgroundColor,
          appBar: widget.showAppBar ? _buildAppBar(context, sizingInformation) : null,
          body: _buildBody(context, sizingInformation),
          bottomNavigationBar: _buildBottomNavigation(context, sizingInformation),
          floatingActionButton: widget.showFloatingActionButton 
              ? widget.buildFloatingActionButton(context) 
              : null,
          drawer: _buildDrawer(context, sizingInformation),
        );
      },
    );
  }

  PreferredSizeWidget? _buildAppBar(BuildContext context, SizingInformation sizingInformation) {
    final customAppBar = widget.buildAppBar(context);
    if (customAppBar != null) return customAppBar;

    final deviceType = sizingInformation.deviceScreenType;
    final height = ResponsiveConfig.getAppBarHeight(deviceType);

    return PreferredSize(
      preferredSize: Size.fromHeight(height),
      child: AppBar(
        title: Text(widget.title),
        actions: [
          ...?widget.appBarActions,
          const ThemeToggleButton(),
          IconButton(
            icon: const Icon(Icons.palette),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const AppearanceCustomizer(),
                ),
              );
            },
            tooltip: 'Customize Appearance',
          ),
        ],
        automaticallyImplyLeading: widget.showBackButton,
        centerTitle: ResponsiveConfig.isMobile(context),
        elevation: ResponsiveConfig.isMobile(context) ? 4 : 0,
      ),
    );
  }

  Widget _buildBody(BuildContext context, SizingInformation sizingInformation) {
    final deviceType = sizingInformation.deviceScreenType;
    final showRail = widget.showNavigationRail && 
                     ResponsiveConfig.shouldShowNavigationRail(deviceType);

    Widget content = _buildResponsiveContent(context, sizingInformation);

    if (widget.centerContent) {
      content = Center(child: content);
    }

    // Apply responsive padding
    final padding = widget.customPadding ?? EdgeInsets.all(
      ResponsiveConfig.getPadding(deviceType),
    );

    content = Padding(
      padding: padding,
      child: content,
    );

    // Apply max width constraint if specified
    final maxWidth = widget.maxContentWidth ?? ResponsiveConfig.getMaxWidth(deviceType);
    if (maxWidth != double.infinity) {
      content = Center(
        child: ConstrainedBox(
          constraints: BoxConstraints(maxWidth: maxWidth),
          child: content,
        ),
      );
    }

    // Add navigation rail if needed
    if (showRail) {
      final navigationRail = widget.buildNavigationRail(context) ?? _buildDefaultNavigationRail(context);
      return Row(
        children: [
          navigationRail,
          const VerticalDivider(thickness: 1, width: 1),
          Expanded(child: content),
        ],
      );
    }

    return content;
  }

  Widget _buildResponsiveContent(BuildContext context, SizingInformation sizingInformation) {
    switch (sizingInformation.deviceScreenType) {
      case DeviceScreenType.mobile:
        return widget.buildMobileContent(context);
      case DeviceScreenType.tablet:
        return widget.buildTabletContent(context);
      case DeviceScreenType.desktop:
        return widget.buildDesktopContent(context);
      default:
        return widget.buildMobileContent(context);
    }
  }

  Widget? _buildBottomNavigation(BuildContext context, SizingInformation sizingInformation) {
    if (!widget.showBottomNavigation) return null;
    
    final deviceType = sizingInformation.deviceScreenType;
    if (!ResponsiveConfig.shouldShowBottomNavigation(deviceType)) return null;

    final customBottomNav = widget.buildBottomNavigation(context);
    if (customBottomNav != null) return customBottomNav;

    final navigationItems = widget.getNavigationItems();
    if (navigationItems.isEmpty) return null;

    return BottomNavigationBar(
      currentIndex: _selectedNavigationIndex,
      onTap: (index) {
        setState(() {
          _selectedNavigationIndex = index;
        });
        widget.onNavigationItemSelected(index);
      },
      type: BottomNavigationBarType.fixed,
      items: navigationItems.map((item) => BottomNavigationBarItem(
        icon: Icon(item.icon),
        label: item.label,
        tooltip: item.tooltip,
      )).toList(),
    );
  }

  Widget? _buildDrawer(BuildContext context, SizingInformation sizingInformation) {
    if (!ResponsiveConfig.isMobile(context)) return null;
    
    final navigationItems = widget.getNavigationItems();
    if (navigationItems.isEmpty) return null;

    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
            ),
            child: Text(
              'MicroRealEstate',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Theme.of(context).colorScheme.onPrimary,
              ),
            ),
          ),
          ...navigationItems.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            
            return ListTile(
              leading: Icon(item.icon),
              title: Text(item.label),
              selected: _selectedNavigationIndex == index,
              onTap: () {
                setState(() {
                  _selectedNavigationIndex = index;
                });
                widget.onNavigationItemSelected(index);
                Navigator.of(context).pop();
              },
            );
          }),
        ],
      ),
    );
  }

  Widget _buildDefaultNavigationRail(BuildContext context) {
    final navigationItems = widget.getNavigationItems();
    if (navigationItems.isEmpty) {
      return const SizedBox(width: 72);
    }

    return NavigationRail(
      selectedIndex: _selectedNavigationIndex,
      onDestinationSelected: (index) {
        setState(() {
          _selectedNavigationIndex = index;
        });
        widget.onNavigationItemSelected(index);
      },
      labelType: NavigationRailLabelType.all,
      destinations: navigationItems.map((item) => NavigationRailDestination(
        icon: Icon(item.icon),
        label: Text(item.label),
      )).toList(),
    );
  }
}

/// Navigation item model
class NavigationItem {
  final IconData icon;
  final String label;
  final String? tooltip;
  final VoidCallback? onTap;

  const NavigationItem({
    required this.icon,
    required this.label,
    this.tooltip,
    this.onTap,
  });
}

/// Responsive layout helper widget
class ResponsiveLayout extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;

  const ResponsiveLayout({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, sizingInformation) {
        switch (sizingInformation.deviceScreenType) {
          case DeviceScreenType.mobile:
            return mobile;
          case DeviceScreenType.tablet:
            return tablet ?? mobile;
          case DeviceScreenType.desktop:
            return desktop ?? tablet ?? mobile;
          default:
            return mobile;
        }
      },
    );
  }
}

/// Responsive value widget
class ResponsiveValue<T> extends StatelessWidget {
  final T mobile;
  final T? tablet;
  final T? desktop;
  final Widget Function(T value) builder;

  const ResponsiveValue({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
    required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, sizingInformation) {
        T value;
        switch (sizingInformation.deviceScreenType) {
          case DeviceScreenType.mobile:
            value = mobile;
            break;
          case DeviceScreenType.tablet:
            value = tablet ?? mobile;
            break;
          case DeviceScreenType.desktop:
            value = desktop ?? tablet ?? mobile;
            break;
          default:
            value = mobile;
        }
        return builder(value);
      },
    );
  }
}
