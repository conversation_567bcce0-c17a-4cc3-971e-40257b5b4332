import 'package:flutter/material.dart';
import 'widgets/framework/framework.dart';
import 'database/databaseservice.dart';
import 'widgets/theme/theme.dart';
import 'widgets/approuteswidget.dart';
import 'pages/homepage.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize framework
  MicroRealEstateFramework.initialize();

  // Initialize theme system
  await MicroRealEstateTheme.initialize();

  // Initialize the database service
  await DatabaseService.instance.initialize();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ThemeProvider(
      child: ListenableBuilder(
        listenable: ThemeManager.instance,
        builder: (context, child) {
          final themeManager = ThemeManager.instance;
          return MaterialApp(
            title: 'MicroRealEstate',
            theme: themeManager.lightTheme,
            darkTheme: themeManager.darkTheme,
            themeMode: themeManager.themeMode,
            initialRoute: AppRoutesWidget.home,
            onGenerateRoute: AppRoutesWidget.generateRoute,
            debugShowCheckedModeBanner: false,
            home: const HomePage(),
          );
        },
      ),
    );
  }
}

