import 'package:flutter/material.dart';
import '../database/pages/databaseviewerpage.dart';
import '../database/widgets/localdatabaseinspector.dart';
import '../widgets/theme/themecustomizer.dart';
import '../widgets/examples/dropdownselect_examples.dart';
import '../widgets/examples/clayexamples.dart';
// import '../pages/dashboard/dashboardpage.dart';
// import '../pages/properties/propertylistpage.dart';
// import '../pages/properties/propertydetailpage.dart';
// import '../pages/tenants/tenantlistpage.dart';
// import '../pages/tenants/tenantdetailpage.dart';
// import '../pages/leases/leaseslistpage.dart';
// import '../pages/leases/leasedetailpage.dart';
// import '../pages/documents/documentslistpage.dart';
// import '../pages/reports/reportspage.dart';
// import '../pages/settings/settingspage.dart';
// import '../pages/auth/loginpage.dart';
// import '../pages/auth/registerpage.dart';

/// App routes configuration and management
class AppRoutesWidget {
  AppRoutesWidget._();

  // Route names
  static const String home = '/';
  static const String dashboard = '/dashboard';
  static const String login = '/login';
  static const String register = '/register';
  
  // Properties routes
  static const String properties = '/properties';
  static const String propertyDetail = '/properties/detail';
  static const String propertyAdd = '/properties/add';
  static const String propertyEdit = '/properties/edit';
  
  // Tenants routes
  static const String tenants = '/tenants';
  static const String tenantDetail = '/tenants/detail';
  static const String tenantAdd = '/tenants/add';
  static const String tenantEdit = '/tenants/edit';
  
  // Leases routes
  static const String leases = '/leases';
  static const String leaseDetail = '/leases/detail';
  static const String leaseAdd = '/leases/add';
  static const String leaseEdit = '/leases/edit';
  
  // Documents routes
  static const String documents = '/documents';
  static const String documentDetail = '/documents/detail';
  static const String documentUpload = '/documents/upload';
  
  // Reports routes
  static const String reports = '/reports';
  static const String reportDetail = '/reports/detail';
  
  // Settings routes
  static const String settings = '/settings';
  static const String themeSettings = '/settings/theme';
  static const String accountSettings = '/settings/account';
  static const String databaseSettings = '/settings/database';
  
  // Development routes
  static const String databaseViewer = '/dev/database-viewer';
  static const String databaseInspector = '/dev/database-inspector';
  static const String frameworkDemo = '/dev/framework-demo';
  static const String themeCustomizer = '/dev/theme-customizer';
  static const String dropdownExamples = '/dev/dropdown-examples';

  /// Generate routes for the app
  static Route<dynamic>? generateRoute(RouteSettings settings) {
    final routeName = settings.name;

    if (routeName == home || routeName == dashboard) {
      return _createRoute(const DashboardPage(), settings);
    } else if (routeName == login) {
      return _createRoute(const LoginPage(), settings);
    } else if (routeName == register) {
      return _createRoute(const RegisterPage(), settings);
    } else if (routeName == properties) {
      return _createRoute(const PropertyListPage(), settings);
    } else if (routeName == propertyDetail) {
      final args = settings.arguments as Map<String, dynamic>?;
      final propertyId = args?['propertyId'] as String?;
      return _createRoute(PropertyDetailPage(propertyId: propertyId), settings);
    } else if (routeName == propertyAdd) {
      return _createRoute(const PropertyDetailPage(), settings);
    } else if (routeName == propertyEdit) {
      final args = settings.arguments as Map<String, dynamic>?;
      final propertyId = args?['propertyId'] as String?;
      return _createRoute(PropertyDetailPage(propertyId: propertyId, isEditing: true), settings);
    } else if (routeName == tenants) {
      return _createRoute(const TenantListPage(), settings);
    } else if (routeName == tenantDetail) {
      final args = settings.arguments as Map<String, dynamic>?;
      final tenantId = args?['tenantId'] as String?;
      return _createRoute(TenantDetailPage(tenantId: tenantId), settings);
    } else if (routeName == tenantAdd) {
      return _createRoute(const TenantDetailPage(), settings);
    } else if (routeName == tenantEdit) {
      final args = settings.arguments as Map<String, dynamic>?;
      final tenantId = args?['tenantId'] as String?;
      return _createRoute(TenantDetailPage(tenantId: tenantId, isEditing: true), settings);
    } else if (routeName == leases) {
      return _createRoute(const LeasesListPage(), settings);
    } else if (routeName == leaseDetail) {
      final args = settings.arguments as Map<String, dynamic>?;
      final leaseId = args?['leaseId'] as String?;
      return _createRoute(LeaseDetailPage(leaseId: leaseId), settings);
    } else if (routeName == leaseAdd) {
      return _createRoute(const LeaseDetailPage(), settings);
    } else if (routeName == leaseEdit) {
      final args = settings.arguments as Map<String, dynamic>?;
      final leaseId = args?['leaseId'] as String?;
      return _createRoute(LeaseDetailPage(leaseId: leaseId, isEditing: true), settings);
    } else if (routeName == documents) {
      return _createRoute(const DocumentsListPage(), settings);
    } else if (routeName == documentDetail) {
      final args = settings.arguments as Map<String, dynamic>?;
      final documentId = args?['documentId'] as String?;
      return _createRoute(DocumentDetailPage(documentId: documentId), settings);
    } else if (routeName == reports) {
      return _createRoute(const ReportsPage(), settings);
    } else if (routeName == reportDetail) {
      final args = settings.arguments as Map<String, dynamic>?;
      final reportType = args?['reportType'] as String?;
      return _createRoute(ReportDetailPage(reportType: reportType), settings);
    } else if (routeName == settings) {
      return _createRoute(const SettingsPage(), settings);
    } else if (routeName == themeSettings) {
      return _createRoute(const AppearanceCustomizer(), settings);
    } else if (routeName == accountSettings) {
      return _createRoute(const AccountSettingsPage(), settings);
    } else if (routeName == databaseSettings) {
      return _createRoute(const DatabaseSettingsPage(), settings);
    } else if (routeName == databaseViewer) {
      return _createRoute(const DatabaseViewerPage(), settings);
    } else if (routeName == databaseInspector) {
      return _createRoute(const LocalDatabaseInspector(), settings);
    } else if (routeName == themeCustomizer) {
      return _createRoute(const AppearanceCustomizer(), settings);
    } else if (routeName == dropdownExamples) {
      return _createRoute(const DropdownSelectExamples(), settings);
    } else {
      return _createRoute(const NotFoundPage(), settings);
    }
  }

  /// Create a route with appropriate transition
  static Route<dynamic> _createRoute(Widget page, RouteSettings settings) {
    return PageRouteBuilder(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(1.0, 0.0);
        const end = Offset.zero;
        const curve = Curves.ease;

        var tween = Tween(begin: begin, end: end).chain(
          CurveTween(curve: curve),
        );

        return SlideTransition(
          position: animation.drive(tween),
          child: child,
        );
      },
      transitionDuration: const Duration(milliseconds: 300),
    );
  }

  /// Navigate to a route
  static Future<T?> navigateTo<T extends Object?>(BuildContext context, String routeName, {Object? arguments}) {
    return Navigator.pushNamed<T>(context, routeName, arguments: arguments);
  }

  /// Navigate and replace current route
  static Future<T?> navigateAndReplace<T extends Object?>(BuildContext context, String routeName, {Object? arguments}) {
    return Navigator.pushReplacementNamed<T, T>(context, routeName, arguments: arguments);
  }

  /// Navigate and clear all previous routes
  static Future<T?> navigateAndClearAll<T extends Object?>(BuildContext context, String routeName, {Object? arguments}) {
    return Navigator.pushNamedAndRemoveUntil<T>(
      context,
      routeName,
      (route) => false,
      arguments: arguments,
    );
  }

  /// Go back to previous route
  static void goBack(BuildContext context, [dynamic result]) {
    Navigator.pop(context, result);
  }

  /// Check if can go back
  static bool canGoBack(BuildContext context) {
    return Navigator.canPop(context);
  }

  /// Navigate to dashboard
  static Future<void> goToDashboard(BuildContext context) {
    return navigateAndClearAll(context, dashboard);
  }

  /// Navigate to login
  static Future<void> goToLogin(BuildContext context) {
    return navigateAndClearAll(context, login);
  }

  /// Navigate to property detail
  static Future<void> goToPropertyDetail(BuildContext context, String propertyId) {
    return navigateTo(context, propertyDetail, arguments: {'propertyId': propertyId});
  }

  /// Navigate to tenant detail
  static Future<void> goToTenantDetail(BuildContext context, String tenantId) {
    return navigateTo(context, tenantDetail, arguments: {'tenantId': tenantId});
  }

  /// Navigate to lease detail
  static Future<void> goToLeaseDetail(BuildContext context, String leaseId) {
    return navigateTo(context, leaseDetail, arguments: {'leaseId': leaseId});
  }

  /// Navigate to add property
  static Future<void> goToAddProperty(BuildContext context) {
    return navigateTo(context, propertyAdd);
  }

  /// Navigate to add tenant
  static Future<void> goToAddTenant(BuildContext context) {
    return navigateTo(context, tenantAdd);
  }

  /// Navigate to add lease
  static Future<void> goToAddLease(BuildContext context) {
    return navigateTo(context, leaseAdd);
  }

  /// Navigate to edit property
  static Future<void> goToEditProperty(BuildContext context, String propertyId) {
    return navigateTo(context, propertyEdit, arguments: {'propertyId': propertyId});
  }

  /// Navigate to edit tenant
  static Future<void> goToEditTenant(BuildContext context, String tenantId) {
    return navigateTo(context, tenantEdit, arguments: {'tenantId': tenantId});
  }

  /// Navigate to edit lease
  static Future<void> goToEditLease(BuildContext context, String leaseId) {
    return navigateTo(context, leaseEdit, arguments: {'leaseId': leaseId});
  }

  /// Navigate to theme customizer
  static Future<void> goToThemeCustomizer(BuildContext context) {
    return navigateTo(context, themeCustomizer);
  }

  /// Navigate to dropdown examples
  static Future<void> goToDropdownExamples(BuildContext context) {
    return navigateTo(context, dropdownExamples);
  }

  /// Navigate to settings
  static Future<void> goToSettings(BuildContext context) {
    return navigateTo(context, settings);
  }

  /// Navigate to reports
  static Future<void> goToReports(BuildContext context) {
    return navigateTo(context, reports);
  }

  /// Navigate to documents
  static Future<void> goToDocuments(BuildContext context) {
    return navigateTo(context, documents);
  }

  /// Get all route names
  static List<String> getAllRoutes() {
    return [
      home,
      dashboard,
      login,
      register,
      properties,
      propertyDetail,
      propertyAdd,
      propertyEdit,
      tenants,
      tenantDetail,
      tenantAdd,
      tenantEdit,
      leases,
      leaseDetail,
      leaseAdd,
      leaseEdit,
      documents,
      documentDetail,
      documentUpload,
      reports,
      reportDetail,
      settings,
      themeSettings,
      accountSettings,
      databaseSettings,
      databaseViewer,
      databaseInspector,
      frameworkDemo,
      themeCustomizer,
    ];
  }

  /// Get route title
  static String getRouteTitle(String routeName) {
    switch (routeName) {
      case home:
      case dashboard:
        return 'Dashboard';
      case login:
        return 'Login';
      case register:
        return 'Register';
      case properties:
        return 'Properties';
      case propertyDetail:
        return 'Property Details';
      case propertyAdd:
        return 'Add Property';
      case propertyEdit:
        return 'Edit Property';
      case tenants:
        return 'Tenants';
      case tenantDetail:
        return 'Tenant Details';
      case tenantAdd:
        return 'Add Tenant';
      case tenantEdit:
        return 'Edit Tenant';
      case leases:
        return 'Leases';
      case leaseDetail:
        return 'Lease Details';
      case leaseAdd:
        return 'Add Lease';
      case leaseEdit:
        return 'Edit Lease';
      case documents:
        return 'Documents';
      case documentDetail:
        return 'Document Details';
      case documentUpload:
        return 'Upload Document';
      case reports:
        return 'Reports';
      case reportDetail:
        return 'Report Details';
      case settings:
        return 'Settings';
      case themeSettings:
        return 'Appearance Settings';
      case accountSettings:
        return 'Account Settings';
      case databaseSettings:
        return 'Database Settings';
      case databaseViewer:
        return 'Database Viewer';
      case databaseInspector:
        return 'Database Inspector';
      case frameworkDemo:
        return 'Framework Demo';
      case themeCustomizer:
        return 'Appearance';
      case dropdownExamples:
        return 'Dropdown Examples';
      default:
        return 'MicroRealEstate';
    }
  }
}

/// 404 Not Found page
class NotFoundPage extends StatelessWidget {
  const NotFoundPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Page Not Found'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              '404 - Page Not Found',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 8),
            const Text('The page you are looking for does not exist.'),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => AppRoutesWidget.goToDashboard(context),
              child: const Text('Go to Dashboard'),
            ),
          ],
        ),
      ),
    );
  }
}

// Placeholder pages that need to be created
class DashboardPage extends StatelessWidget {
  const DashboardPage({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Dashboard Page - To be implemented')));
}

class PropertyListPage extends StatelessWidget {
  const PropertyListPage({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Property List Page - To be implemented')));
}

class PropertyDetailPage extends StatelessWidget {
  final String? propertyId;
  final bool isEditing;
  const PropertyDetailPage({super.key, this.propertyId, this.isEditing = false});
  @override
  Widget build(BuildContext context) => Scaffold(body: Center(child: Text('Property Detail Page - To be implemented\nProperty ID: $propertyId\nEditing: $isEditing')));
}

class TenantListPage extends StatelessWidget {
  const TenantListPage({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Tenant List Page - To be implemented')));
}

class TenantDetailPage extends StatelessWidget {
  final String? tenantId;
  final bool isEditing;
  const TenantDetailPage({super.key, this.tenantId, this.isEditing = false});
  @override
  Widget build(BuildContext context) => Scaffold(body: Center(child: Text('Tenant Detail Page - To be implemented\nTenant ID: $tenantId\nEditing: $isEditing')));
}

class LeasesListPage extends StatelessWidget {
  const LeasesListPage({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Leases List Page - To be implemented')));
}

class LeaseDetailPage extends StatelessWidget {
  final String? leaseId;
  final bool isEditing;
  const LeaseDetailPage({super.key, this.leaseId, this.isEditing = false});
  @override
  Widget build(BuildContext context) => Scaffold(body: Center(child: Text('Lease Detail Page - To be implemented\nLease ID: $leaseId\nEditing: $isEditing')));
}

class DocumentsListPage extends StatelessWidget {
  const DocumentsListPage({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Documents List Page - To be implemented')));
}

class DocumentDetailPage extends StatelessWidget {
  final String? documentId;
  const DocumentDetailPage({super.key, this.documentId});
  @override
  Widget build(BuildContext context) => Scaffold(body: Center(child: Text('Document Detail Page - To be implemented\nDocument ID: $documentId')));
}

class ReportsPage extends StatelessWidget {
  const ReportsPage({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Reports Page - To be implemented')));
}

class ReportDetailPage extends StatelessWidget {
  final String? reportType;
  const ReportDetailPage({super.key, this.reportType});
  @override
  Widget build(BuildContext context) => Scaffold(body: Center(child: Text('Report Detail Page - To be implemented\nReport Type: $reportType')));
}

class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Settings Page - To be implemented')));
}

class AccountSettingsPage extends StatelessWidget {
  const AccountSettingsPage({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Account Settings Page - To be implemented')));
}

class DatabaseSettingsPage extends StatelessWidget {
  const DatabaseSettingsPage({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Database Settings Page - To be implemented')));
}

class LoginPage extends StatelessWidget {
  const LoginPage({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Login Page - To be implemented')));
}

class RegisterPage extends StatelessWidget {
  const RegisterPage({super.key});
  @override
  Widget build(BuildContext context) => const Scaffold(body: Center(child: Text('Register Page - To be implemented')));
}

/// Navigation helper widget for consistent navigation throughout the app
class AppNavigationHelper extends StatelessWidget {
  final Widget child;

  const AppNavigationHelper({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return child;
  }

  /// Show navigation drawer with all available routes
  static void showNavigationDrawer(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => const NavigationDrawerWidget(),
    );
  }

  /// Show quick navigation menu
  static void showQuickNavigation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const QuickNavigationDialog(),
    );
  }
}

/// Navigation drawer widget
class NavigationDrawerWidget extends StatelessWidget {
  const NavigationDrawerWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Navigation',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              _buildNavChip(context, 'Dashboard', AppRoutesWidget.dashboard, Icons.dashboard),
              _buildNavChip(context, 'Properties', AppRoutesWidget.properties, Icons.home),
              _buildNavChip(context, 'Tenants', AppRoutesWidget.tenants, Icons.people),
              _buildNavChip(context, 'Leases', AppRoutesWidget.leases, Icons.description),
              _buildNavChip(context, 'Documents', AppRoutesWidget.documents, Icons.folder),
              _buildNavChip(context, 'Reports', AppRoutesWidget.reports, Icons.analytics),
              _buildNavChip(context, 'Settings', AppRoutesWidget.settings, Icons.settings),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'Development',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              _buildNavChip(context, 'DB Viewer', AppRoutesWidget.databaseViewer, Icons.storage),
              _buildNavChip(context, 'DB Inspector', AppRoutesWidget.databaseInspector, Icons.search),
              _buildNavChip(context, 'Framework Demo', AppRoutesWidget.frameworkDemo, Icons.web),
              _buildNavChip(context, 'Appearance', AppRoutesWidget.themeCustomizer, Icons.palette),
              _buildNavChip(context, 'Dropdown Examples', AppRoutesWidget.dropdownExamples, Icons.arrow_drop_down_circle),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNavChip(BuildContext context, String label, String route, IconData icon) {
    return ActionChip(
      avatar: Icon(icon, size: 18),
      label: Text(label),
      onPressed: () {
        Navigator.pop(context);
        AppRoutesWidget.navigateTo(context, route);
      },
    );
  }
}

/// Quick navigation dialog
class QuickNavigationDialog extends StatefulWidget {
  const QuickNavigationDialog({super.key});

  @override
  State<QuickNavigationDialog> createState() => _QuickNavigationDialogState();
}

class _QuickNavigationDialogState extends State<QuickNavigationDialog> {
  final TextEditingController _searchController = TextEditingController();
  List<String> _filteredRoutes = [];

  @override
  void initState() {
    super.initState();
    _filteredRoutes = AppRoutesWidget.getAllRoutes();
    _searchController.addListener(_filterRoutes);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterRoutes() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredRoutes = AppRoutesWidget.getAllRoutes()
          .where((route) => AppRoutesWidget.getRouteTitle(route).toLowerCase().contains(query))
          .toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Quick Navigation'),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'Search routes...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 300,
              child: ListView.builder(
                itemCount: _filteredRoutes.length,
                itemBuilder: (context, index) {
                  final route = _filteredRoutes[index];
                  final title = AppRoutesWidget.getRouteTitle(route);

                  return ListTile(
                    title: Text(title),
                    subtitle: Text(route),
                    onTap: () {
                      Navigator.pop(context);
                      AppRoutesWidget.navigateTo(context, route);
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
      ],
    );
  }
}
