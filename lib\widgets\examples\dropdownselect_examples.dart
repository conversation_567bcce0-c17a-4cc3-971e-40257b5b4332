import 'package:flutter/material.dart';
import '../dropdownselect.dart';

/// Examples demonstrating various uses of the DropdownSelect widget
class DropdownSelectExamples extends StatefulWidget {
  const DropdownSelectExamples({super.key});

  @override
  State<DropdownSelectExamples> createState() => _DropdownSelectExamplesState();
}

class _DropdownSelectExamplesState extends State<DropdownSelectExamples> {
  ThemeMode? selectedThemeMode = ThemeMode.system;
  String? selectedFont = 'System';
  String? selectedLanguage = 'en';
  String? selectedCurrency = 'USD';
  String? selectedCountry;
  String? selectedPriority;
  String? selectedStatus;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('DropdownSelect Examples'),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Basic Examples Section
          _buildSectionHeader('Basic Examples'),
          const SizedBox(height: 16),
          
          // Theme Mode Dropdown
          DropdownSelectFactory.themeMode(
            value: selectedThemeMode,
            onChanged: (value) => setState(() => selectedThemeMode = value),
          ),
          const SizedBox(height: 16),
          
          // Font Family Dropdown
          DropdownSelectFactory.fontFamily(
            value: selectedFont,
            onChanged: (value) => setState(() => selectedFont = value),
          ),
          const SizedBox(height: 16),
          
          // Language Dropdown
          DropdownSelectFactory.language(
            value: selectedLanguage,
            onChanged: (value) => setState(() => selectedLanguage = value),
          ),
          const SizedBox(height: 16),
          
          // Currency Dropdown
          DropdownSelectFactory.currency(
            value: selectedCurrency,
            onChanged: (value) => setState(() => selectedCurrency = value),
          ),
          const SizedBox(height: 32),
          
          // Custom Styled Examples Section
          _buildSectionHeader('Custom Styled Examples'),
          const SizedBox(height: 16),
          
          // Filled Style Country Dropdown
          DropdownSelect<String>(
            value: selectedCountry,
            onChanged: (value) => setState(() => selectedCountry = value),
            decoration: DropdownSelectStyles.filled(
              labelText: 'Country',
              hintText: 'Select your country',
              prefixIcon: const Icon(Icons.flag),
              fillColor: Theme.of(context).colorScheme.surfaceVariant,
            ),
            items: const [
              DropdownSelectItem(
                value: 'US',
                title: 'United States',
                leading: Text('🇺🇸'),
              ),
              DropdownSelectItem(
                value: 'CA',
                title: 'Canada',
                leading: Text('🇨🇦'),
              ),
              DropdownSelectItem(
                value: 'UK',
                title: 'United Kingdom',
                leading: Text('🇬🇧'),
              ),
              DropdownSelectItem(
                value: 'DE',
                title: 'Germany',
                leading: Text('🇩🇪'),
              ),
              DropdownSelectItem(
                value: 'FR',
                title: 'France',
                leading: Text('🇫🇷'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Compact Style Priority Dropdown
          DropdownSelect<String>(
            value: selectedPriority,
            onChanged: (value) => setState(() => selectedPriority = value),
            decoration: DropdownSelectStyles.compact(
              labelText: 'Priority',
              prefixIcon: const Icon(Icons.priority_high),
            ),
            items: const [
              DropdownSelectItem(
                value: 'low',
                title: 'Low',
                leading: Icon(Icons.keyboard_arrow_down, color: Colors.green),
                trailing: Chip(
                  label: Text('Low', style: TextStyle(fontSize: 10)),
                  backgroundColor: Colors.green,
                ),
              ),
              DropdownSelectItem(
                value: 'medium',
                title: 'Medium',
                leading: Icon(Icons.remove, color: Colors.orange),
                trailing: Chip(
                  label: Text('Med', style: TextStyle(fontSize: 10)),
                  backgroundColor: Colors.orange,
                ),
              ),
              DropdownSelectItem(
                value: 'high',
                title: 'High',
                leading: Icon(Icons.keyboard_arrow_up, color: Colors.red),
                trailing: Chip(
                  label: Text('High', style: TextStyle(fontSize: 10)),
                  backgroundColor: Colors.red,
                ),
              ),
              DropdownSelectItem(
                value: 'urgent',
                title: 'Urgent',
                subtitle: 'Requires immediate attention',
                leading: Icon(Icons.warning, color: Colors.red),
                trailing: Icon(Icons.flash_on, color: Colors.red),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Underlined Style Status Dropdown
          DropdownSelect<String>(
            value: selectedStatus,
            onChanged: (value) => setState(() => selectedStatus = value),
            decoration: DropdownSelectStyles.underlined(
              labelText: 'Status',
              prefixIcon: const Icon(Icons.info_outline),
            ),
            items: const [
              DropdownSelectItem(
                value: 'draft',
                title: 'Draft',
                subtitle: 'Work in progress',
                leading: Icon(Icons.edit, color: Colors.grey),
              ),
              DropdownSelectItem(
                value: 'pending',
                title: 'Pending Review',
                subtitle: 'Waiting for approval',
                leading: Icon(Icons.schedule, color: Colors.orange),
              ),
              DropdownSelectItem(
                value: 'approved',
                title: 'Approved',
                subtitle: 'Ready to publish',
                leading: Icon(Icons.check_circle, color: Colors.green),
              ),
              DropdownSelectItem(
                value: 'rejected',
                title: 'Rejected',
                subtitle: 'Needs revision',
                leading: Icon(Icons.cancel, color: Colors.red),
              ),
              DropdownSelectItem(
                value: 'archived',
                title: 'Archived',
                subtitle: 'No longer active',
                leading: Icon(Icons.archive, color: Colors.grey),
                enabled: false,
              ),
            ],
          ),
          const SizedBox(height: 32),
          
          // Advanced Features Section
          _buildSectionHeader('Advanced Features'),
          const SizedBox(height: 16),
          
          // Dropdown with validation
          DropdownSelect<String>(
            value: null,
            onChanged: (value) {},
            decoration: DropdownSelectStyles.outlined(
              labelText: 'Required Field',
              hintText: 'Please select an option',
              prefixIcon: const Icon(Icons.star, color: Colors.red),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'This field is required';
              }
              return null;
            },
            autovalidateMode: AutovalidateMode.onUserInteraction,
            items: const [
              DropdownSelectItem(value: 'option1', title: 'Option 1'),
              DropdownSelectItem(value: 'option2', title: 'Option 2'),
              DropdownSelectItem(value: 'option3', title: 'Option 3'),
            ],
          ),
          const SizedBox(height: 16),
          
          // Disabled dropdown
          DropdownSelect<String>(
            value: 'disabled',
            onChanged: null,
            enabled: false,
            decoration: DropdownSelectStyles.outlined(
              labelText: 'Disabled Dropdown',
              prefixIcon: const Icon(Icons.lock),
            ),
            items: const [
              DropdownSelectItem(value: 'disabled', title: 'This is disabled'),
            ],
          ),
          const SizedBox(height: 32),
          
          // Current Selections Display
          _buildSectionHeader('Current Selections'),
          const SizedBox(height: 16),
          _buildSelectionSummary(),
        ],
      ),
    );
  }
  
  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
        fontWeight: FontWeight.bold,
        color: Theme.of(context).colorScheme.primary,
      ),
    );
  }
  
  Widget _buildSelectionSummary() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Selected Values:',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text('Theme Mode: ${selectedThemeMode?.name ?? 'None'}'),
            Text('Font: $selectedFont'),
            Text('Language: $selectedLanguage'),
            Text('Currency: $selectedCurrency'),
            Text('Country: ${selectedCountry ?? 'None'}'),
            Text('Priority: ${selectedPriority ?? 'None'}'),
            Text('Status: ${selectedStatus ?? 'None'}'),
          ],
        ),
      ),
    );
  }
}
