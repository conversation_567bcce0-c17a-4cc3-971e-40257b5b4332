import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'themeconfig.dart';

/// Theme manager for handling app-wide theme state and persistence
class ThemeManager extends ChangeNotifier {
  static ThemeManager? _instance;
  static ThemeManager get instance => _instance ??= ThemeManager._();
  
  ThemeManager._() {
    _loadThemeSettings();
  }

  // Theme state
  ThemeMode _themeMode = ThemeMode.system;
  Color _primaryColor = ThemeConfig.defaultPrimary;
  Color _secondaryColor = ThemeConfig.defaultSecondary;
  bool _useMaterial3 = true;
  String _fontFamily = 'System';
  bool _highContrastMode = false;
  double _textScaleFactor = 1.0;

  // Getters
  ThemeMode get themeMode => _themeMode;
  Color get primaryColor => _primaryColor;
  Color get secondaryColor => _secondaryColor;
  bool get useMaterial3 => _useMaterial3;
  String get fontFamily => _fontFamily;
  bool get highContrastMode => _highContrastMode;
  double get textScaleFactor => _textScaleFactor;

  // Theme data getters
  ThemeData get lightTheme {
    var theme = ThemeConfig.lightTheme(
      primaryColor: _primaryColor,
      fontFamily: _fontFamily,
      useMaterial3: _useMaterial3,
    );

    // Apply accessibility modifications if needed
    if (_highContrastMode) {
      final highContrastScheme = ThemeConfig.createHighContrastColorScheme(
        seedColor: _primaryColor,
        brightness: Brightness.light,
      );
      theme = theme.copyWith(colorScheme: highContrastScheme);
    }

    // Apply text scale factor
    if (_textScaleFactor != 1.0) {
      theme = theme.copyWith(
        textTheme: theme.textTheme.apply(fontSizeFactor: _textScaleFactor),
      );
    }

    return theme;
  }

  ThemeData get darkTheme {
    var theme = ThemeConfig.darkTheme(
      primaryColor: _primaryColor,
      fontFamily: _fontFamily,
      useMaterial3: _useMaterial3,
    );

    // Apply accessibility modifications if needed
    if (_highContrastMode) {
      final highContrastScheme = ThemeConfig.createHighContrastColorScheme(
        seedColor: _primaryColor,
        brightness: Brightness.dark,
      );
      theme = theme.copyWith(colorScheme: highContrastScheme);
    }

    // Apply text scale factor
    if (_textScaleFactor != 1.0) {
      theme = theme.copyWith(
        textTheme: theme.textTheme.apply(fontSizeFactor: _textScaleFactor),
      );
    }

    return theme;
  }

  /// Set theme mode (light, dark, system)
  Future<void> setThemeMode(ThemeMode mode) async {
    if (_themeMode != mode) {
      _themeMode = mode;
      await _saveThemeSettings();
      notifyListeners();
    }
  }

  /// Set primary color
  Future<void> setPrimaryColor(Color color) async {
    if (_primaryColor != color) {
      _primaryColor = color;
      await _saveThemeSettings();
      notifyListeners();
    }
  }

  /// Set secondary color
  Future<void> setSecondaryColor(Color color) async {
    if (_secondaryColor != color) {
      _secondaryColor = color;
      await _saveThemeSettings();
      notifyListeners();
    }
  }

  /// Toggle Material 3 design
  Future<void> setUseMaterial3(bool useMaterial3) async {
    if (_useMaterial3 != useMaterial3) {
      _useMaterial3 = useMaterial3;
      await _saveThemeSettings();
      notifyListeners();
    }
  }

  /// Set font family
  Future<void> setFontFamily(String fontFamily) async {
    if (_fontFamily != fontFamily && _isValidFontFamily(fontFamily)) {
      _fontFamily = fontFamily;
      await _saveThemeSettings();
      notifyListeners();
    }
  }

  /// Toggle high contrast mode
  Future<void> setHighContrastMode(bool enabled) async {
    if (_highContrastMode != enabled) {
      _highContrastMode = enabled;
      await _saveThemeSettings();
      notifyListeners();
    }
  }

  /// Set text scale factor
  Future<void> setTextScaleFactor(double scaleFactor) async {
    final clampedScale = scaleFactor.clamp(0.8, 2.0);
    if (_textScaleFactor != clampedScale) {
      _textScaleFactor = clampedScale;
      await _saveThemeSettings();
      notifyListeners();
    }
  }

  /// Reset to default theme
  Future<void> resetToDefault() async {
    _themeMode = ThemeMode.system;
    _primaryColor = ThemeConfig.defaultPrimary;
    _secondaryColor = ThemeConfig.defaultSecondary;
    _useMaterial3 = true;
    _fontFamily = 'System';
    _highContrastMode = false;
    _textScaleFactor = 1.0;
    await _saveThemeSettings();
    notifyListeners();
  }

  /// Apply predefined color scheme
  Future<void> applyColorScheme(String schemeName) async {
    final color = ThemeConfig.colorSchemes[schemeName];
    if (color != null) {
      await setPrimaryColor(color);
    }
  }

  /// Get current theme brightness
  Brightness getCurrentBrightness(BuildContext context) {
    switch (_themeMode) {
      case ThemeMode.light:
        return Brightness.light;
      case ThemeMode.dark:
        return Brightness.dark;
      case ThemeMode.system:
        return MediaQuery.of(context).platformBrightness;
    }
  }

  /// Check if current theme is dark
  bool isDarkMode(BuildContext context) {
    return getCurrentBrightness(context) == Brightness.dark;
  }

  /// Get theme mode display name
  String getThemeModeDisplayName() {
    switch (_themeMode) {
      case ThemeMode.light:
        return 'Light';
      case ThemeMode.dark:
        return 'Dark';
      case ThemeMode.system:
        return 'System';
    }
  }

  /// Load theme settings from shared preferences
  Future<void> _loadThemeSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Load theme mode with validation
      final themeModeIndex = prefs.getInt('theme_mode') ?? 0;
      if (themeModeIndex >= 0 && themeModeIndex < ThemeMode.values.length) {
        _themeMode = ThemeMode.values[themeModeIndex];
      } else {
        _themeMode = ThemeMode.system;
        debugPrint('Invalid theme mode index: $themeModeIndex, using system default');
      }

      // Load primary color with validation
      final primaryColorValue = prefs.getInt('primary_color') ?? ThemeConfig.defaultPrimary.value;
      if (_isValidColorValue(primaryColorValue)) {
        _primaryColor = Color(primaryColorValue);
      } else {
        _primaryColor = ThemeConfig.defaultPrimary;
        debugPrint('Invalid primary color value: $primaryColorValue, using default');
      }

      // Load secondary color with validation
      final secondaryColorValue = prefs.getInt('secondary_color') ?? ThemeConfig.defaultSecondary.value;
      if (_isValidColorValue(secondaryColorValue)) {
        _secondaryColor = Color(secondaryColorValue);
      } else {
        _secondaryColor = ThemeConfig.defaultSecondary;
        debugPrint('Invalid secondary color value: $secondaryColorValue, using default');
      }

      // Load Material 3 setting
      _useMaterial3 = prefs.getBool('use_material3') ?? true;

      // Load font family with validation
      final fontFamily = prefs.getString('font_family') ?? 'System';
      if (_isValidFontFamily(fontFamily)) {
        _fontFamily = fontFamily;
      } else {
        _fontFamily = 'System';
        debugPrint('Invalid font family: $fontFamily, using system default');
      }

      // Load accessibility settings
      _highContrastMode = prefs.getBool('high_contrast_mode') ?? false;

      final textScale = prefs.getDouble('text_scale_factor') ?? 1.0;
      _textScaleFactor = textScale.clamp(0.8, 2.0);

      notifyListeners();
    } catch (e) {
      debugPrint('Error loading theme settings: $e');
      // Reset to defaults on error
      await _resetToDefaultsInternal();
    }
  }

  /// Validate color value
  bool _isValidColorValue(int colorValue) {
    return colorValue >= 0x00000000 && colorValue <= 0xFFFFFFFF;
  }

  /// Validate font family
  bool _isValidFontFamily(String fontFamily) {
    const validFontFamilies = [
      'System',
      'Roboto',
      'Open Sans',
      'Lato',
      'Montserrat',
      'Poppins',
      'Inter',
      'Source Sans Pro',
    ];
    return validFontFamilies.contains(fontFamily);
  }

  /// Internal reset method without saving
  Future<void> _resetToDefaultsInternal() async {
    _themeMode = ThemeMode.system;
    _primaryColor = ThemeConfig.defaultPrimary;
    _secondaryColor = ThemeConfig.defaultSecondary;
    _useMaterial3 = true;
    _fontFamily = 'System';
    _highContrastMode = false;
    _textScaleFactor = 1.0;
    notifyListeners();
  }

  /// Get responsive spacing for current screen
  double getResponsiveSpacing(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    return ThemeConfig.getResponsiveSpacing(screenWidth);
  }

  /// Get device type for current screen
  DeviceType getDeviceType(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    return ThemeConfig.getDeviceType(screenWidth);
  }

  /// Check if current theme meets accessibility contrast requirements
  bool meetsAccessibilityStandards(BuildContext context) {
    final theme = Theme.of(context);
    return ThemeConfig.meetsContrastRequirements(
      theme.colorScheme.onSurface,
      theme.colorScheme.surface,
    );
  }

  /// Save theme settings to shared preferences
  Future<void> _saveThemeSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      await prefs.setInt('theme_mode', _themeMode.index);
      await prefs.setInt('primary_color', _primaryColor.value);
      await prefs.setInt('secondary_color', _secondaryColor.value);
      await prefs.setBool('use_material3', _useMaterial3);
      await prefs.setString('font_family', _fontFamily);
      await prefs.setBool('high_contrast_mode', _highContrastMode);
      await prefs.setDouble('text_scale_factor', _textScaleFactor);
    } catch (e) {
      debugPrint('Error saving theme settings: $e');
    }
  }

  /// Export theme settings as JSON
  Map<String, dynamic> exportThemeSettings() {
    return {
      'theme_mode': _themeMode.index,
      'primary_color': _primaryColor.value,
      'secondary_color': _secondaryColor.value,
      'use_material3': _useMaterial3,
      'font_family': _fontFamily,
      'high_contrast_mode': _highContrastMode,
      'text_scale_factor': _textScaleFactor,
      'exported_at': DateTime.now().toIso8601String(),
      'version': '2.0', // Version for compatibility
    };
  }

  /// Import theme settings from JSON
  Future<void> importThemeSettings(Map<String, dynamic> settings) async {
    try {
      // Validate theme mode
      if (settings.containsKey('theme_mode')) {
        final themeModeIndex = settings['theme_mode'] as int;
        if (themeModeIndex >= 0 && themeModeIndex < ThemeMode.values.length) {
          _themeMode = ThemeMode.values[themeModeIndex];
        }
      }

      // Validate and import colors
      if (settings.containsKey('primary_color')) {
        final colorValue = settings['primary_color'] as int;
        if (_isValidColorValue(colorValue)) {
          _primaryColor = Color(colorValue);
        }
      }

      if (settings.containsKey('secondary_color')) {
        final colorValue = settings['secondary_color'] as int;
        if (_isValidColorValue(colorValue)) {
          _secondaryColor = Color(colorValue);
        }
      }

      // Import other settings
      if (settings.containsKey('use_material3')) {
        _useMaterial3 = settings['use_material3'] as bool;
      }

      if (settings.containsKey('font_family')) {
        final fontFamily = settings['font_family'] as String;
        if (_isValidFontFamily(fontFamily)) {
          _fontFamily = fontFamily;
        }
      }

      // Import accessibility settings (version 2.0+)
      if (settings.containsKey('high_contrast_mode')) {
        _highContrastMode = settings['high_contrast_mode'] as bool;
      }

      if (settings.containsKey('text_scale_factor')) {
        final scale = settings['text_scale_factor'] as double;
        _textScaleFactor = scale.clamp(0.8, 2.0);
      }

      await _saveThemeSettings();
      notifyListeners();
    } catch (e) {
      debugPrint('Error importing theme settings: $e');
      // Don't reset on import error, just log it
    }
  }

  /// Get theme statistics
  Map<String, dynamic> getThemeStatistics() {
    return {
      'current_theme_mode': getThemeModeDisplayName(),
      'primary_color_hex': '#${_primaryColor.value.toRadixString(16).padLeft(8, '0').substring(2)}',
      'secondary_color_hex': '#${_secondaryColor.value.toRadixString(16).padLeft(8, '0').substring(2)}',
      'using_material3': _useMaterial3,
      'font_family': _fontFamily,
      'high_contrast_mode': _highContrastMode,
      'text_scale_factor': _textScaleFactor,
      'available_color_schemes': ThemeConfig.colorSchemes.length,
      'available_font_families': ThemeConfig.availableFontFamilies.length,
    };
  }

  /// Create a custom color scheme from primary color
  ColorScheme createCustomColorScheme({
    required Color primaryColor,
    required Brightness brightness,
  }) {
    return ColorScheme.fromSeed(
      seedColor: primaryColor,
      brightness: brightness,
    );
  }

  /// Get complementary color for the current primary color
  Color getComplementaryColor() {
    final hsl = HSLColor.fromColor(_primaryColor);
    final complementaryHue = (hsl.hue + 180) % 360;
    return hsl.withHue(complementaryHue).toColor();
  }

  /// Get analogous colors for the current primary color
  List<Color> getAnalogousColors() {
    final hsl = HSLColor.fromColor(_primaryColor);
    return [
      hsl.withHue((hsl.hue + 30) % 360).toColor(),
      hsl.withHue((hsl.hue - 30) % 360).toColor(),
    ];
  }

  /// Get triadic colors for the current primary color
  List<Color> getTriadicColors() {
    final hsl = HSLColor.fromColor(_primaryColor);
    return [
      hsl.withHue((hsl.hue + 120) % 360).toColor(),
      hsl.withHue((hsl.hue + 240) % 360).toColor(),
    ];
  }
}
