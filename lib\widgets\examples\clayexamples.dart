import 'package:flutter/material.dart';
import '../clay/claywidgets.dart';
import '../clay/clayrealestate.dart';
import '../theme/claytheme.dart';

/// Examples demonstrating clay containers and widgets
class ClayExamples extends StatefulWidget {
  const ClayExamples({super.key});

  @override
  State<ClayExamples> createState() => _ClayExamplesState();
}

class _ClayExamplesState extends State<ClayExamples> {
  bool switchValue = false;
  double sliderValue = 0.5;
  int selectedNavIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ClayTheme.getClayColorForSurface(
        context,
        surface: ClayThemeSurface.background,
      ),
      appBar: AppBar(
        title: const Text('Clay Design Examples'),
        backgroundColor: ClayTheme.getClayColorForSurface(
          context,
          surface: ClayThemeSurface.surface,
        ),
        elevation: 0,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Basic Clay Widgets Section
          _buildSectionHeader('Basic Clay Widgets'),
          const SizedBox(height: 16),
          
          // Buttons
          Wrap(
            spacing: 12,
            runSpacing: 12,
            children: [
              ClayButton(
                onPressed: () {},
                child: const Text('Clay Button'),
              ),
              ClayButton(
                onPressed: () {},
                surface: ClayThemeSurface.elevated,
                child: const Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.add),
                    SizedBox(width: 8),
                    Text('With Icon'),
                  ],
                ),
              ),
              ClayButton(
                onPressed: null,
                child: const Text('Disabled'),
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Cards
          ClayCard(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Clay Card',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 8),
                Text(
                  'This is a clay card with neumorphic design. It provides a soft, elegant appearance that feels tactile and modern.',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Interactive Controls
          ClayCard(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Interactive Controls',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 16),
                
                // Switch
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        'Clay Switch',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                    ),
                    ClaySwitch(
                      value: switchValue,
                      onChanged: (value) => setState(() => switchValue = value),
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                // Slider
                Text(
                  'Clay Slider: ${(sliderValue * 100).round()}%',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                ClaySlider(
                  value: sliderValue,
                  onChanged: (value) => setState(() => sliderValue = value),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Text Field
          ClayTextField(
            labelText: 'Clay Text Field',
            hintText: 'Enter some text...',
            prefixIcon: const Icon(Icons.search),
          ),
          
          const SizedBox(height: 32),
          
          // Real Estate Widgets Section
          _buildSectionHeader('Real Estate Clay Widgets'),
          const SizedBox(height: 16),
          
          // Property Card
          ClayPropertyCard(
            title: 'Modern Downtown Apartment',
            subtitle: '2 bed • 2 bath • 1,200 sq ft',
            price: '\$2,500/mo',
            location: '123 Main St, Downtown',
            image: Container(
              color: Colors.grey.shade300,
              child: const Center(
                child: Icon(
                  Icons.home,
                  size: 64,
                  color: Colors.grey,
                ),
              ),
            ),
            badges: const [
              ClayBadge(
                label: 'Pet Friendly',
                icon: Icon(Icons.pets, size: 12),
              ),
              ClayBadge(
                label: 'Parking',
                icon: Icon(Icons.local_parking, size: 12),
              ),
              ClayBadge(
                label: 'Gym',
                icon: Icon(Icons.fitness_center, size: 12),
              ),
            ],
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Property card tapped!')),
              );
            },
          ),
          
          const SizedBox(height: 16),
          
          // Stat Cards
          Row(
            children: [
              Expanded(
                child: ClayStatCard(
                  title: 'Total Properties',
                  value: '156',
                  subtitle: '+12 this month',
                  icon: const Icon(Icons.home_work),
                  color: Colors.blue.shade100,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ClayStatCard(
                  title: 'Revenue',
                  value: '\$45.2K',
                  subtitle: '+8.5% from last month',
                  icon: const Icon(Icons.attach_money),
                  color: Colors.green.shade100,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Navigation Tiles
          ...List.generate(3, (index) {
            final titles = ['Properties', 'Tenants', 'Maintenance'];
            final subtitles = ['Manage listings', 'Tenant information', 'Service requests'];
            final icons = [Icons.home, Icons.people, Icons.build];
            
            return ClayNavigationTile(
              title: titles[index],
              subtitle: subtitles[index],
              icon: Icon(icons[index]),
              selected: selectedNavIndex == index,
              margin: const EdgeInsets.only(bottom: 8),
              onTap: () => setState(() => selectedNavIndex = index),
            );
          }),
          
          const SizedBox(height: 16),
          
          // Action Buttons
          Row(
            children: [
              Expanded(
                child: ClayActionButton(
                  label: 'Add Property',
                  icon: const Icon(Icons.add_home),
                  onPressed: () {},
                  color: Colors.blue.shade50,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ClayActionButton(
                  label: 'View Reports',
                  icon: const Icon(Icons.analytics),
                  onPressed: () {},
                  color: Colors.purple.shade50,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 32),
          
          // Different Depths Section
          _buildSectionHeader('Different Clay Depths'),
          const SizedBox(height: 16),
          
          Row(
            children: [
              Expanded(
                child: ClayTheme.createFlatContainer(
                  context: context,
                  padding: const EdgeInsets.all(16),
                  child: const Center(child: Text('Flat')),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ClayTheme.createSubtleContainer(
                  context: context,
                  padding: const EdgeInsets.all(16),
                  child: const Center(child: Text('Subtle')),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ClayTheme.createContainer(
                  context: context,
                  padding: const EdgeInsets.all(16),
                  child: const Center(child: Text('Medium')),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ClayTheme.createFloatingContainer(
                  context: context,
                  padding: const EdgeInsets.all(16),
                  child: const Center(child: Text('Deep')),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Pressed vs Normal
          Row(
            children: [
              Expanded(
                child: ClayTheme.createContainer(
                  context: context,
                  padding: const EdgeInsets.all(16),
                  child: const Center(child: Text('Normal')),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ClayTheme.createPressedContainer(
                  context: context,
                  padding: const EdgeInsets.all(16),
                  child: const Center(child: Text('Pressed')),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 32),
        ],
      ),
      floatingActionButton: ClayFloatingActionButton(
        onPressed: () {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Clay FAB pressed!')),
          );
        },
        child: const Icon(Icons.add),
      ),
    );
  }
  
  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
        fontWeight: FontWeight.bold,
        color: Theme.of(context).colorScheme.primary,
      ),
    );
  }
}
