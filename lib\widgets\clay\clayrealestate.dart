import 'package:flutter/material.dart';
import '../theme/claytheme.dart';
import 'claywidgets.dart';

/// Clay-themed property card for real estate listings
class ClayPropertyCard extends StatelessWidget {
  final String title;
  final String? subtitle;
  final String? price;
  final String? location;
  final Widget? image;
  final List<Widget>? badges;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? margin;

  const ClayPropertyCard({
    super.key,
    required this.title,
    this.subtitle,
    this.price,
    this.location,
    this.image,
    this.badges,
    this.onTap,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return ClayCard(
      margin: margin,
      padding: EdgeInsets.zero,
      onTap: onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Image section
          if (image != null)
            ClipRRect(
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(ClayTheme.radiusMedium),
              ),
              child: Sized<PERSON><PERSON>(
                height: 200,
                width: double.infinity,
                child: image!,
              ),
            ),
          
          // Content section
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title and price row
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        title,
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (price != null) ...[
                      const SizedBox(width: 8),
                      ClayCard(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        surface: ClayThemeSurface.elevated,
                        depth: ClayTheme.depthSubtle,
                        borderRadius: ClayTheme.radiusSmall,
                        child: Text(
                          price!,
                          style: theme.textTheme.titleMedium?.copyWith(
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                
                // Subtitle
                if (subtitle != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    subtitle!,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.7),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
                
                // Location
                if (location != null) ...[
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(
                        Icons.location_on,
                        size: 16,
                        color: theme.colorScheme.onSurface.withOpacity(0.6),
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          location!,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurface.withOpacity(0.6),
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ],
                
                // Badges
                if (badges != null && badges!.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  Wrap(
                    spacing: 8,
                    runSpacing: 4,
                    children: badges!,
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// Clay-themed dashboard stat card
class ClayStatCard extends StatelessWidget {
  final String title;
  final String value;
  final String? subtitle;
  final Widget? icon;
  final Color? color;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? margin;

  const ClayStatCard({
    super.key,
    required this.title,
    required this.value,
    this.subtitle,
    this.icon,
    this.color,
    this.onTap,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return ClayCard(
      margin: margin,
      onTap: onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with icon
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: theme.textTheme.titleMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
              if (icon != null)
                ClayCard(
                  padding: const EdgeInsets.all(8),
                  surface: ClayThemeSurface.elevated,
                  depth: ClayTheme.depthSubtle,
                  borderRadius: ClayTheme.radiusSmall,
                  color: color,
                  child: icon!,
                ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // Value
          Text(
            value,
            style: theme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color ?? theme.colorScheme.primary,
            ),
          ),
          
          // Subtitle
          if (subtitle != null) ...[
            const SizedBox(height: 4),
            Text(
              subtitle!,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Clay-themed navigation tile
class ClayNavigationTile extends StatelessWidget {
  final String title;
  final String? subtitle;
  final Widget icon;
  final VoidCallback? onTap;
  final bool selected;
  final EdgeInsetsGeometry? margin;

  const ClayNavigationTile({
    super.key,
    required this.title,
    this.subtitle,
    required this.icon,
    this.onTap,
    this.selected = false,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return ClayCard(
      margin: margin,
      onTap: onTap,
      surface: selected ? ClayThemeSurface.elevated : ClayThemeSurface.surface,
      depth: selected ? ClayTheme.depthPressed : ClayTheme.depthMedium,
      color: selected ? theme.colorScheme.primaryContainer : null,
      child: Row(
        children: [
          // Icon
          ClayCard(
            padding: const EdgeInsets.all(12),
            surface: ClayThemeSurface.elevated,
            depth: ClayTheme.depthSubtle,
            borderRadius: ClayTheme.radiusSmall,
            child: icon,
          ),
          
          const SizedBox(width: 16),
          
          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: selected 
                        ? theme.colorScheme.onPrimaryContainer
                        : theme.colorScheme.onSurface,
                  ),
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: 2),
                  Text(
                    subtitle!,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: selected
                          ? theme.colorScheme.onPrimaryContainer.withOpacity(0.7)
                          : theme.colorScheme.onSurface.withOpacity(0.6),
                    ),
                  ),
                ],
              ],
            ),
          ),
          
          // Arrow
          Icon(
            Icons.chevron_right,
            color: selected
                ? theme.colorScheme.onPrimaryContainer.withOpacity(0.7)
                : theme.colorScheme.onSurface.withOpacity(0.4),
          ),
        ],
      ),
    );
  }
}

/// Clay-themed action button for quick actions
class ClayActionButton extends StatelessWidget {
  final String label;
  final Widget icon;
  final VoidCallback? onPressed;
  final Color? color;
  final EdgeInsetsGeometry? margin;

  const ClayActionButton({
    super.key,
    required this.label,
    required this.icon,
    this.onPressed,
    this.color,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return ClayButton(
      onPressed: onPressed,
      margin: margin,
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      color: color,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          icon,
          const SizedBox(width: 8),
          Text(
            label,
            style: theme.textTheme.labelLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}

/// Clay-themed badge for property features
class ClayBadge extends StatelessWidget {
  final String label;
  final Widget? icon;
  final Color? color;
  final EdgeInsetsGeometry? margin;

  const ClayBadge({
    super.key,
    required this.label,
    this.icon,
    this.color,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return ClayCard(
      margin: margin,
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      surface: ClayThemeSurface.elevated,
      depth: ClayTheme.depthSubtle,
      borderRadius: ClayTheme.radiusSmall,
      color: color,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null) ...[
            icon!,
            const SizedBox(width: 4),
          ],
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
